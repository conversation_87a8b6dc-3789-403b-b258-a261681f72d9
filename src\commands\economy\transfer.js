const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('تحويل')
    .setDescription('تحويل الأموال إلى مستخدم آخر')
    .addUserOption(option => 
      option.setName('مستخدم')
        .setDescription('المستخدم المراد تحويل الأموال إليه')
        .setRequired(true))
    .addIntegerOption(option => 
      option.setName('المبلغ')
        .setDescription('المبلغ المراد تحويله')
        .setRequired(true)
        .setMinValue(1)),
  
  async execute(interaction) {
    await interaction.deferReply();
    
    const targetUser = interaction.options.getUser('مستخدم');
    const amount = interaction.options.getInteger('المبلغ');
    
    // التحقق من عدم تحويل الأموال للنفس
    if (targetUser.id === interaction.user.id) {
      return interaction.editReply('❌ لا يمكنك تحويل الأموال لنفسك!');
    }
    
    try {
      // البحث عن حساب المرسل
      let senderProfile = await User.findOne({ userId: interaction.user.id });
      
      if (!senderProfile) {
        // إنشاء حساب جديد للمرسل إذا لم يكن موجودًا
        senderProfile = await User.create({
          userId: interaction.user.id,
          username: interaction.user.username,
          balance: 1000,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // التحقق من وجود رصيد كافٍ
      if (senderProfile.balance < amount) {
        return interaction.editReply(`❌ ليس لديك رصيد كافٍ! رصيدك الحالي هو ${formatNumber(senderProfile.balance)} 💲`);
      }
      
      // البحث عن حساب المستلم
      let receiverProfile = await User.findOne({ userId: targetUser.id });
      
      if (!receiverProfile) {
        // إنشاء حساب جديد للمستلم إذا لم يكن موجودًا
        receiverProfile = await User.create({
          userId: targetUser.id,
          username: targetUser.username,
          balance: 1000,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // إجراء عملية التحويل
      senderProfile.balance -= amount;
      receiverProfile.balance += amount;
      
      // حفظ التغييرات
      await senderProfile.save();
      await receiverProfile.save();
      
      // تسجيل المعاملة
      logTransaction({
        type: 'transfer',
        senderId: interaction.user.id,
        senderName: interaction.user.username,
        receiverId: targetUser.id,
        receiverName: targetUser.username,
        amount: amount,
        timestamp: new Date()
      });
      
      // إنشاء رسالة تأكيد
      const embed = new EmbedBuilder()
        .setTitle('💸 تحويل ناجح')
        .setColor('#4CAF50')
        .setDescription(`تم تحويل ${formatNumber(amount)} 💲 إلى ${targetUser.username} بنجاح!`)
        .addFields(
          { name: 'رصيدك المتبقي', value: `${formatNumber(senderProfile.balance)} 💲`, inline: true }
        )
        .setFooter({ text: `معرف المعاملة: ${Date.now().toString(36).toUpperCase()}` })
        .setTimestamp();
      
      await interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error('خطأ في أمر التحويل:', error);
      await interaction.editReply('حدث خطأ أثناء تنفيذ عملية التحويل. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
