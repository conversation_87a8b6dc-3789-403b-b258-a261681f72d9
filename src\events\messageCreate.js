const { EmbedBuilder } = require('discord.js');
const { logTransaction } = require('../utils/logger');

module.exports = {
  name: 'messageCreate',
  async execute(client, message) {
    // تجاهل الرسائل من البوتات
    if (message.author.bot) return;
    
    // التحقق من البرفكس
    const prefix = client.config.prefix;
    
    // التحقق ما إذا كانت الرسالة تبدأ بالبرفكس أو أن البوت تم الإشارة إليه
    if (!message.content.startsWith(prefix) && !message.content.includes(`<@${client.user.id}>`)) return;
    
    // الحصول على الأمر والوسيطات
    let args;
    let commandName;
    
    // إذا كان المستخدم يستخدم الإشارة إلى البوت بدلاً من البرفكس
    if (message.content.includes(`<@${client.user.id}>`)) {
      args = message.content.slice(message.content.indexOf('>') + 1).trim().split(/ +/);
      commandName = args.shift().toLowerCase();
      
      // إذا كانت رسالة الإشارة فقط، أرسل رسالة المساعدة
      if (!commandName) {
        const helpEmbed = new EmbedBuilder()
          .setColor('#0099ff')
          .setTitle('🎮 مرحباً بك في بوت كازينو البنك المتطور!')
          .setDescription(`أنا هنا لمساعدتك في لعب ألعاب الكازينو وإدارة أموالك!`)
          .addFields(
            { name: 'البرفكس', value: `\`${prefix}\``, inline: true },
            { name: 'مثال', value: `\`${prefix}رصيد\``, inline: true },
            { name: 'للمساعدة', value: `اكتب \`${prefix}مساعدة\` للحصول على قائمة بجميع الأوامر.` }
          )
          .setFooter({ text: '💰 استمتع باللعب بمسؤولية!' })
          .setTimestamp();
        
        return message.reply({ embeds: [helpEmbed] });
      }
    } else {
      args = message.content.slice(prefix.length).trim().split(/ +/);
      commandName = args.shift().toLowerCase();
    }
    
    // البحث عن الأمر
    const command = client.commands.get(commandName) || client.commands.get(client.aliases.get(commandName));
    
    // إذا لم يتم العثور على الأمر
    if (!command) return;
    
    // التحقق من صلاحيات المستخدم إذا كان الأمر يتطلب صلاحيات خاصة
    if (command.permissions && !message.member.permissions.has(command.permissions)) {
      return message.reply('❌ ليس لديك الصلاحيات اللازمة لتنفيذ هذا الأمر!');
    }
    
    // التحقق من صلاحيات المسؤول إذا كان الأمر للمسؤولين فقط
    if (command.adminOnly && !client.config.adminIds.includes(message.author.id)) {
      return message.reply('❌ هذا الأمر للمسؤولين فقط!');
    }
    
    // التحقق من وقت الانتظار (cooldown)
    if (command.cooldown) {
      const cooldowns = client.cooldowns;
      
      if (!cooldowns.has(command.name)) {
        cooldowns.set(command.name, new Map());
      }
      
      const now = Date.now();
      const timestamps = cooldowns.get(command.name);
      const cooldownAmount = (command.cooldown || 3) * 1000;
      
      if (timestamps.has(message.author.id)) {
        const expirationTime = timestamps.get(message.author.id) + cooldownAmount;
        
        if (now < expirationTime) {
          const timeLeft = (expirationTime - now) / 1000;
          return message.reply(`🕒 الرجاء الانتظار ${timeLeft.toFixed(1)} ثوانٍ قبل استخدام الأمر \`${prefix}${command.name}\` مرة أخرى.`);
        }
      }
      
      timestamps.set(message.author.id, now);
      setTimeout(() => timestamps.delete(message.author.id), cooldownAmount);
    }
    
    // تنفيذ الأمر
    try {
      await command.execute(message, args, client);
    } catch (error) {
      console.error(`Error executing command ${command.name}:`, error);
      message.reply('❌ حدث خطأ أثناء تنفيذ هذا الأمر. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
