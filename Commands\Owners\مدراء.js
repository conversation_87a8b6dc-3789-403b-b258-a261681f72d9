const Data = require('pro.db');
const { MessageEmbed } = require('discord.js');
const { owners } = require(`${process.cwd()}/config`);

module.exports = {
    name: "المدير",
    description: "عرض قائمة المديرين الأسبوعيين.",
    run: async (client, message, args) => {
        if (!owners.includes(message.author.id)) return message.react('❌');

        try {
            const managerID = Data.get(`weeklyManager_${message.guild.id}`);
            if (!managerID) return message.reply("لا يوجد مدير أسبوعي معين حالياً.");

            const managerUser = await client.users.fetch(managerID);
            if (!managerUser) return message.reply("تعذر العثور على المستخدم.");

            const embed = new MessageEmbed()
                .setTitle("قائمة المدراء الأسبوعيين")
                .setDescription(`**المدير الحالي هو: <@${managerUser.id}>**`)
                .setThumbnail('https://l.top4top.io/p_3127vfwar1.png')
                .setColor("#000")
                .setFooter({ text: 'Sky Store', iconURL: 'https://k.top4top.io/p_3299aejba1.png' });

            message.channel.send({ embeds: [embed] });
        } catch (error) {
            console.error(error);
            message.reply("حدث خطأ أثناء محاولة عرض قائمة المديرين.");
        }
    }
};
