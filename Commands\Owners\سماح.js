const Data = require('pro.db');
const { MessageEmbed } = require('discord.js');
const { owners } = require(`${process.cwd()}/config`);

module.exports = {
    name: "سماح",
    description: "يقوم بفك منع الأشخاص المحظورين من القيام بشيء معين.",
    run: async (client, message, args) => {
        if (!owners.includes(message.author.id)) return message.react('❌');

        let mentionedUser;

        if (message.mentions.users.size > 0) {
            mentionedUser = message.mentions.users.first();
        } else {
            const userId = args[0];
            if (!userId) return message.reply("**يرجى إرفاق منشن الشخص أو الآيدي**");

            mentionedUser = await client.users.fetch(userId);
            if (!mentionedUser) return message.reply("❌ لم أتمكن من العثور على المستخدم.");
        }

        const isBlocked = await Data.get(`blocked_${mentionedUser.id}`);
        if (!isBlocked) return message.react("🤔");

        await Data.delete(`blocked_${mentionedUser.id}`);

        message.react("☑️");

        // تسجيل اللوج
        const logChannelId = await Data.get(`logChannel_${message.guild.id}`);
        const logChannel = message.guild.channels.cache.get(logChannelId);

        if (logChannel) {
            const logEmbed = new MessageEmbed()
                .setTitle("رفع المنع")
                .setDescription(`**تم رفع الحظر عن المستخدم <@${mentionedUser.id}> 
                    من قبل : <@${message.author.id}>**`)
                .setThumbnail('https://c.top4top.io/p_3127tx5jo1.png')
               
                .setColor("#000")
                .setFooter({ text: 'Sky Store', iconURL: 'https://k.top4top.io/p_3299aejba1.png' });
        
            logChannel.send({ embeds: [logEmbed] });
        }        
    }
};
