import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import { <PERSON><PERSON>, HttpHandlerOptions as __HttpHandlerOptions, MetadataBearer as __Metada<PERSON>earer, MiddlewareStack } from "@aws-sdk/types";
import { PutBucketEncryptionRequest } from "../models/models_0";
import { S3ClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../S3Client";
/**
 * @public
 *
 * The input for {@link PutBucketEncryptionCommand}.
 */
export interface PutBucketEncryptionCommandInput extends PutBucketEncryptionRequest {
}
/**
 * @public
 *
 * The output of {@link PutBucketEncryptionCommand}.
 */
export interface PutBucketEncryptionCommandOutput extends __MetadataBearer {
}
/**
 * @public
 * <p>This action uses the <code>encryption</code> subresource to configure default encryption
 *          and Amazon S3 Bucket Keys for an existing bucket.</p>
 *          <p>By default, all buckets have a default encryption configuration that
 *          uses server-side encryption with Amazon S3 managed keys (SSE-S3).
 *          You can optionally configure default encryption for a bucket by using server-side
 *          encryption with an Amazon Web Services KMS key (SSE-KMS) or a customer-provided key (SSE-C). If you specify default encryption by using
 *          SSE-KMS, you can also configure Amazon S3 Bucket Keys. For information about bucket default encryption,
 *          see <a href="https://docs.aws.amazon.com/AmazonS3/latest/dev/bucket-encryption.html">Amazon S3
 *             bucket default encryption</a> in the <i>Amazon S3 User Guide</i>. For more
 *          information about S3 Bucket Keys, see <a href="https://docs.aws.amazon.com/AmazonS3/latest/dev/bucket-key.html">Amazon S3 Bucket Keys</a> in the
 *             <i>Amazon S3 User Guide</i>.</p>
 *          <important>
 *             <p>This action requires Amazon Web Services Signature Version 4. For more information, see <a href="https://docs.aws.amazon.com/AmazonS3/latest/API/sig-v4-authenticating-requests.html">
 *                Authenticating Requests (Amazon Web Services Signature Version 4)</a>. </p>
 *          </important>
 *          <p>To use this operation, you must have permissions to perform the
 *             <code>s3:PutEncryptionConfiguration</code> action. The bucket owner has this permission
 *          by default. The bucket owner can grant this permission to others. For more information
 *          about permissions, see <a href="https://docs.aws.amazon.com/AmazonS3/latest/userguide/using-with-s3-actions.html#using-with-s3-actions-related-to-bucket-subresources">Permissions Related to Bucket Subresource Operations</a> and <a href="https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-access-control.html">Managing
 *             Access Permissions to Your Amazon S3 Resources</a> in the
 *             <i>Amazon S3 User Guide</i>. </p>
 *          <p>The following operations are related to <code>PutBucketEncryption</code>:</p>
 *          <ul>
 *             <li>
 *                <p>
 *                   <a href="https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketEncryption.html">GetBucketEncryption</a>
 *                </p>
 *             </li>
 *             <li>
 *                <p>
 *                   <a href="https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucketEncryption.html">DeleteBucketEncryption</a>
 *                </p>
 *             </li>
 *          </ul>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { S3Client, PutBucketEncryptionCommand } from "@aws-sdk/client-s3"; // ES Modules import
 * // const { S3Client, PutBucketEncryptionCommand } = require("@aws-sdk/client-s3"); // CommonJS import
 * const client = new S3Client(config);
 * const input = { // PutBucketEncryptionRequest
 *   Bucket: "STRING_VALUE", // required
 *   ContentMD5: "STRING_VALUE",
 *   ChecksumAlgorithm: "CRC32" || "CRC32C" || "SHA1" || "SHA256",
 *   ServerSideEncryptionConfiguration: { // ServerSideEncryptionConfiguration
 *     Rules: [ // ServerSideEncryptionRules // required
 *       { // ServerSideEncryptionRule
 *         ApplyServerSideEncryptionByDefault: { // ServerSideEncryptionByDefault
 *           SSEAlgorithm: "AES256" || "aws:kms", // required
 *           KMSMasterKeyID: "STRING_VALUE",
 *         },
 *         BucketKeyEnabled: true || false,
 *       },
 *     ],
 *   },
 *   ExpectedBucketOwner: "STRING_VALUE",
 * };
 * const command = new PutBucketEncryptionCommand(input);
 * const response = await client.send(command);
 * // {};
 *
 * ```
 *
 * @param PutBucketEncryptionCommandInput - {@link PutBucketEncryptionCommandInput}
 * @returns {@link PutBucketEncryptionCommandOutput}
 * @see {@link PutBucketEncryptionCommandInput} for command's `input` shape.
 * @see {@link PutBucketEncryptionCommandOutput} for command's `response` shape.
 * @see {@link S3ClientResolvedConfig | config} for S3Client's `config` shape.
 *
 * @throws {@link S3ServiceException}
 * <p>Base exception class for all service exceptions from S3 service.</p>
 *
 */
export declare class PutBucketEncryptionCommand extends $Command<PutBucketEncryptionCommandInput, PutBucketEncryptionCommandOutput, S3ClientResolvedConfig> {
    readonly input: PutBucketEncryptionCommandInput;
    static getEndpointParameterInstructions(): EndpointParameterInstructions;
    /**
     * @public
     */
    constructor(input: PutBucketEncryptionCommandInput);
    /**
     * @internal
     */
    resolveMiddleware(clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>, configuration: S3ClientResolvedConfig, options?: __HttpHandlerOptions): Handler<PutBucketEncryptionCommandInput, PutBucketEncryptionCommandOutput>;
    /**
     * @internal
     */
    private serialize;
    /**
     * @internal
     */
    private deserialize;
}
