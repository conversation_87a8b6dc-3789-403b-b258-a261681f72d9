declare const _default: "<!--\n        DTD for XML Schemas: Part 2: Datatypes\n        $Id: datatypes.dtd,v 1.23 2001/03/16 17:36:30 ht Exp $\n        Note this DTD is NOT normative, or even definitive. - - the\n        prose copy in the datatypes REC is the definitive version\n        (which shouldn't differ from this one except for this comment\n        and entity expansions, but just in case)\n  -->\n\n<!--\n        This DTD cannot be used on its own, it is intended\n        only for incorporation in XMLSchema.dtd, q.v.\n  -->\n\n<!-- Define all the element names, with optional prefix -->\n<!ENTITY % simpleType \"%p;simpleType\">\n<!ENTITY % restriction \"%p;restriction\">\n<!ENTITY % list \"%p;list\">\n<!ENTITY % union \"%p;union\">\n<!ENTITY % maxExclusive \"%p;maxExclusive\">\n<!ENTITY % minExclusive \"%p;minExclusive\">\n<!ENTITY % maxInclusive \"%p;maxInclusive\">\n<!ENTITY % minInclusive \"%p;minInclusive\">\n<!ENTITY % totalDigits \"%p;totalDigits\">\n<!ENTITY % fractionDigits \"%p;fractionDigits\">\n<!ENTITY % length \"%p;length\">\n<!ENTITY % minLength \"%p;minLength\">\n<!ENTITY % maxLength \"%p;maxLength\">\n<!ENTITY % enumeration \"%p;enumeration\">\n<!ENTITY % whiteSpace \"%p;whiteSpace\">\n<!ENTITY % pattern \"%p;pattern\">\n\n<!--\n        Customisation entities for the ATTLIST of each element\n        type. Define one of these if your schema takes advantage\n        of the anyAttribute='##other' in the schema for schemas\n  -->\n\n<!ENTITY % simpleTypeAttrs \"\">\n<!ENTITY % restrictionAttrs \"\">\n<!ENTITY % listAttrs \"\">\n<!ENTITY % unionAttrs \"\">\n<!ENTITY % maxExclusiveAttrs \"\">\n<!ENTITY % minExclusiveAttrs \"\">\n<!ENTITY % maxInclusiveAttrs \"\">\n<!ENTITY % minInclusiveAttrs \"\">\n<!ENTITY % totalDigitsAttrs \"\">\n<!ENTITY % fractionDigitsAttrs \"\">\n<!ENTITY % lengthAttrs \"\">\n<!ENTITY % minLengthAttrs \"\">\n<!ENTITY % maxLengthAttrs \"\">\n<!ENTITY % enumerationAttrs \"\">\n<!ENTITY % whiteSpaceAttrs \"\">\n<!ENTITY % patternAttrs \"\">\n\n<!-- Define some entities for informative use as attribute\n        types -->\n<!ENTITY % URIref \"CDATA\">\n<!ENTITY % XPathExpr \"CDATA\">\n<!ENTITY % QName \"NMTOKEN\">\n<!ENTITY % QNames \"NMTOKENS\">\n<!ENTITY % NCName \"NMTOKEN\">\n<!ENTITY % nonNegativeInteger \"NMTOKEN\">\n<!ENTITY % boolean \"(true|false)\">\n<!ENTITY % simpleDerivationSet \"CDATA\">\n<!--\n        #all or space-separated list drawn from derivationChoice\n  -->\n\n<!--\n        Note that the use of 'facet' below is less restrictive\n        than is really intended:  There should in fact be no\n        more than one of each of minInclusive, minExclusive,\n        maxInclusive, maxExclusive, totalDigits, fractionDigits,\n        length, maxLength, minLength within datatype,\n        and the min- and max- variants of Inclusive and Exclusive\n        are mutually exclusive. On the other hand,  pattern and\n        enumeration may repeat.\n  -->\n<!ENTITY % minBound \"(%minInclusive; | %minExclusive;)\">\n<!ENTITY % maxBound \"(%maxInclusive; | %maxExclusive;)\">\n<!ENTITY % bounds \"%minBound; | %maxBound;\">\n<!ENTITY % numeric \"%totalDigits; | %fractionDigits;\">\n<!ENTITY % ordered \"%bounds; | %numeric;\">\n<!ENTITY % unordered\n   \"%pattern; | %enumeration; | %whiteSpace; | %length; |\n   %maxLength; | %minLength;\">\n<!ENTITY % facet \"%ordered; | %unordered;\">\n<!ENTITY % facetAttr \n        \"value CDATA #REQUIRED\n        id ID #IMPLIED\">\n<!ENTITY % fixedAttr \"fixed %boolean; #IMPLIED\">\n<!ENTITY % facetModel \"(%annotation;)?\">\n<!ELEMENT %simpleType;\n        ((%annotation;)?, (%restriction; | %list; | %union;))>\n<!ATTLIST %simpleType;\n    name      %NCName; #IMPLIED\n    final     %simpleDerivationSet; #IMPLIED\n    id        ID       #IMPLIED\n    %simpleTypeAttrs;>\n<!-- name is required at top level -->\n<!ELEMENT %restriction; ((%annotation;)?,\n                         (%restriction1; |\n                          ((%simpleType;)?,(%facet;)*)),\n                         (%attrDecls;))>\n<!ATTLIST %restriction;\n    base      %QName;                  #IMPLIED\n    id        ID       #IMPLIED\n    %restrictionAttrs;>\n<!--\n        base and simpleType child are mutually exclusive,\n        one is required.\n\n        restriction is shared between simpleType and\n        simpleContent and complexContent (in XMLSchema.xsd).\n        restriction1 is for the latter cases, when this\n        is restricting a complex type, as is attrDecls.\n  -->\n<!ELEMENT %list; ((%annotation;)?,(%simpleType;)?)>\n<!ATTLIST %list;\n    itemType      %QName;             #IMPLIED\n    id        ID       #IMPLIED\n    %listAttrs;>\n<!--\n        itemType and simpleType child are mutually exclusive,\n        one is required\n  -->\n<!ELEMENT %union; ((%annotation;)?,(%simpleType;)*)>\n<!ATTLIST %union;\n    id            ID       #IMPLIED\n    memberTypes   %QNames;            #IMPLIED\n    %unionAttrs;>\n<!--\n        At least one item in memberTypes or one simpleType\n        child is required\n  -->\n\n<!ELEMENT %maxExclusive; %facetModel;>\n<!ATTLIST %maxExclusive;\n        %facetAttr;\n        %fixedAttr;\n        %maxExclusiveAttrs;>\n<!ELEMENT %minExclusive; %facetModel;>\n<!ATTLIST %minExclusive;\n        %facetAttr;\n        %fixedAttr;\n        %minExclusiveAttrs;>\n\n<!ELEMENT %maxInclusive; %facetModel;>\n<!ATTLIST %maxInclusive;\n        %facetAttr;\n        %fixedAttr;\n        %maxInclusiveAttrs;>\n<!ELEMENT %minInclusive; %facetModel;>\n<!ATTLIST %minInclusive;\n        %facetAttr;\n        %fixedAttr;\n        %minInclusiveAttrs;>\n\n<!ELEMENT %totalDigits; %facetModel;>\n<!ATTLIST %totalDigits;\n        %facetAttr;\n        %fixedAttr;\n        %totalDigitsAttrs;>\n<!ELEMENT %fractionDigits; %facetModel;>\n<!ATTLIST %fractionDigits;\n        %facetAttr;\n        %fixedAttr;\n        %fractionDigitsAttrs;>\n\n<!ELEMENT %length; %facetModel;>\n<!ATTLIST %length;\n        %facetAttr;\n        %fixedAttr;\n        %lengthAttrs;>\n<!ELEMENT %minLength; %facetModel;>\n<!ATTLIST %minLength;\n        %facetAttr;\n        %fixedAttr;\n        %minLengthAttrs;>\n<!ELEMENT %maxLength; %facetModel;>\n<!ATTLIST %maxLength;\n        %facetAttr;\n        %fixedAttr;\n        %maxLengthAttrs;>\n\n<!-- This one can be repeated -->\n<!ELEMENT %enumeration; %facetModel;>\n<!ATTLIST %enumeration;\n        %facetAttr;\n        %enumerationAttrs;>\n\n<!ELEMENT %whiteSpace; %facetModel;>\n<!ATTLIST %whiteSpace;\n        %facetAttr;\n        %fixedAttr;\n        %whiteSpaceAttrs;>\n\n<!-- This one can be repeated -->\n<!ELEMENT %pattern; %facetModel;>\n<!ATTLIST %pattern;\n        %facetAttr;\n        %patternAttrs;>";
export default _default;
