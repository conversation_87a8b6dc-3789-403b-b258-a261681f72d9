"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRuntimeConfig = void 0;
const tslib_1 = require("tslib");
const package_json_1 = tslib_1.__importDefault(require("../package.json"));
const client_sts_1 = require("@aws-sdk/client-sts");
const config_resolver_1 = require("@aws-sdk/config-resolver");
const credential_provider_node_1 = require("@aws-sdk/credential-provider-node");
const eventstream_serde_node_1 = require("@aws-sdk/eventstream-serde-node");
const hash_node_1 = require("@aws-sdk/hash-node");
const hash_stream_node_1 = require("@aws-sdk/hash-stream-node");
const middleware_bucket_endpoint_1 = require("@aws-sdk/middleware-bucket-endpoint");
const middleware_retry_1 = require("@aws-sdk/middleware-retry");
const node_config_provider_1 = require("@aws-sdk/node-config-provider");
const node_http_handler_1 = require("@aws-sdk/node-http-handler");
const util_body_length_node_1 = require("@aws-sdk/util-body-length-node");
const util_retry_1 = require("@aws-sdk/util-retry");
const util_stream_node_1 = require("@aws-sdk/util-stream-node");
const util_user_agent_node_1 = require("@aws-sdk/util-user-agent-node");
const runtimeConfig_shared_1 = require("./runtimeConfig.shared");
const smithy_client_1 = require("@aws-sdk/smithy-client");
const util_defaults_mode_node_1 = require("@aws-sdk/util-defaults-mode-node");
const smithy_client_2 = require("@aws-sdk/smithy-client");
const getRuntimeConfig = (config) => {
    (0, smithy_client_2.emitWarningIfUnsupportedVersion)(process.version);
    const defaultsMode = (0, util_defaults_mode_node_1.resolveDefaultsModeConfig)(config);
    const defaultConfigProvider = () => defaultsMode().then(smithy_client_1.loadConfigsForDefaultMode);
    const clientSharedValues = (0, runtimeConfig_shared_1.getRuntimeConfig)(config);
    return {
        ...clientSharedValues,
        ...config,
        runtime: "node",
        defaultsMode,
        bodyLengthChecker: config?.bodyLengthChecker ?? util_body_length_node_1.calculateBodyLength,
        credentialDefaultProvider: config?.credentialDefaultProvider ?? (0, client_sts_1.decorateDefaultCredentialProvider)(credential_provider_node_1.defaultProvider),
        defaultUserAgentProvider: config?.defaultUserAgentProvider ??
            (0, util_user_agent_node_1.defaultUserAgent)({ serviceId: clientSharedValues.serviceId, clientVersion: package_json_1.default.version }),
        eventStreamSerdeProvider: config?.eventStreamSerdeProvider ?? eventstream_serde_node_1.eventStreamSerdeProvider,
        getAwsChunkedEncodingStream: config?.getAwsChunkedEncodingStream ?? util_stream_node_1.getAwsChunkedEncodingStream,
        maxAttempts: config?.maxAttempts ?? (0, node_config_provider_1.loadConfig)(middleware_retry_1.NODE_MAX_ATTEMPT_CONFIG_OPTIONS),
        md5: config?.md5 ?? hash_node_1.Hash.bind(null, "md5"),
        region: config?.region ?? (0, node_config_provider_1.loadConfig)(config_resolver_1.NODE_REGION_CONFIG_OPTIONS, config_resolver_1.NODE_REGION_CONFIG_FILE_OPTIONS),
        requestHandler: config?.requestHandler ?? new node_http_handler_1.NodeHttpHandler(defaultConfigProvider),
        retryMode: config?.retryMode ??
            (0, node_config_provider_1.loadConfig)({
                ...middleware_retry_1.NODE_RETRY_MODE_CONFIG_OPTIONS,
                default: async () => (await defaultConfigProvider()).retryMode || util_retry_1.DEFAULT_RETRY_MODE,
            }),
        sdkStreamMixin: config?.sdkStreamMixin ?? util_stream_node_1.sdkStreamMixin,
        sha1: config?.sha1 ?? hash_node_1.Hash.bind(null, "sha1"),
        sha256: config?.sha256 ?? hash_node_1.Hash.bind(null, "sha256"),
        streamCollector: config?.streamCollector ?? node_http_handler_1.streamCollector,
        streamHasher: config?.streamHasher ?? hash_stream_node_1.readableStreamHasher,
        useArnRegion: config?.useArnRegion ?? (0, node_config_provider_1.loadConfig)(middleware_bucket_endpoint_1.NODE_USE_ARN_REGION_CONFIG_OPTIONS),
        useDualstackEndpoint: config?.useDualstackEndpoint ?? (0, node_config_provider_1.loadConfig)(config_resolver_1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS),
        useFipsEndpoint: config?.useFipsEndpoint ?? (0, node_config_provider_1.loadConfig)(config_resolver_1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS),
    };
};
exports.getRuntimeConfig = getRuntimeConfig;
