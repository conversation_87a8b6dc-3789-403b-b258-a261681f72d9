{"name": "@authenio/samlify-node-xmllint", "version": "2.0.0", "description": "Validator module powered by node-xmllint", "main": "build/index.js", "scripts": {"build": "yarn run clean && tsc --diagnostics", "clean": "rm -rf build"}, "repository": {"type": "git", "url": "git+https://github.com/authenio/samlify-node-xmllint.git"}, "author": "tngan", "license": "MIT", "bugs": {"url": "https://github.com/authenio/samlify-node-xmllint/issues"}, "homepage": "https://github.com/authenio/samlify-node-xmllint#readme", "dependencies": {"node-xmllint": "^1.0.0"}, "devDependencies": {"@types/node": "^12.0.4", "typescript": "^3.5.1"}, "peerDependencies": {"samlify": ">= 2.6.0"}}