const { <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

// تعريف قيم بطاقات اللعب
const cardValues = {
  'A': 11, // قد يكون 1 أو 11 حسب اللعبة
  '2': 2,
  '3': 3,
  '4': 4,
  '5': 5,
  '6': 6,
  '7': 7,
  '8': 8,
  '9': 9,
  '10': 10,
  'J': 10,
  'Q': 10,
  'K': 10
};

// تعريف أنواع البطاقات
const cardSuits = ['♠️', '♥️', '♦️', '♣️'];

// إنشاء حزمة بطاقات
function createDeck() {
  const deck = [];
  for (const suit of cardSuits) {
    for (const value in cardValues) {
      deck.push({ suit, value });
    }
  }
  return deck;
}

// خلط البطاقات
function shuffleDeck(deck) {
  for (let i = deck.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [deck[i], deck[j]] = [deck[j], deck[i]];
  }
  return deck;
}

// سحب بطاقة من الحزمة
function drawCard(deck) {
  return deck.pop();
}

// حساب مجموع النقاط
function calculateHandValue(hand) {
  let sum = 0;
  let aceCount = 0;
  
  for (const card of hand) {
    sum += cardValues[card.value];
    if (card.value === 'A') {
      aceCount++;
    }
  }
  
  // تعديل قيمة الآس إذا كان المجموع أكبر من 21
  while (sum > 21 && aceCount > 0) {
    sum -= 10; // تحويل قيمة الآس من 11 إلى 1
    aceCount--;
  }
  
  return sum;
}

// تحويل اليد إلى سلسلة نصية للعرض
function formatHand(hand) {
  return hand.map(card => `${card.value}${card.suit}`).join(' ');
}

// تخزين حالات اللعبة الجارية
const activeGames = new Map();

module.exports = {
  data: new SlashCommandBuilder()
    .setName('بلاك_جاك')
    .setDescription('لعب البلاك جاك (21) بمبلغ معين')
    .addIntegerOption(option => 
      option.setName('المبلغ')
        .setDescription('المبلغ المراد المراهنة به')
        .setRequired(true)
        .setMinValue(100)),
  
  async execute(interaction) {
    if (activeGames.has(interaction.user.id)) {
      return interaction.reply({ content: '❌ لديك لعبة بلاك جاك نشطة بالفعل! أنهِ اللعبة الحالية أولاً.', ephemeral: true });
    }
    
    await interaction.deferReply();
    
    const amount = interaction.options.getInteger('المبلغ');
    
    try {
      // البحث عن حساب المستخدم
      let userProfile = await User.findOne({ userId: interaction.user.id });
      
      if (!userProfile) {
        // إنشاء حساب جديد للمستخدم إذا لم يكن موجودًا
        userProfile = await User.create({
          userId: interaction.user.id,
          username: interaction.user.username,
          balance: 1000,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // التحقق من وجود رصيد كافٍ
      if (userProfile.balance < amount) {
        return interaction.editReply(`❌ ليس لديك رصيد كافٍ! رصيدك الحالي هو ${formatNumber(userProfile.balance)} 💲`);
      }
      
      // خصم المبلغ مقدمًا
      userProfile.balance -= amount;
      await userProfile.save();
      
      // إنشاء وخلط حزمة البطاقات
      const deck = shuffleDeck(createDeck());
      
      // سحب البطاقات الأولية
      const playerHand = [drawCard(deck), drawCard(deck)];
      const dealerHand = [drawCard(deck), drawCard(deck)];
      
      // حساب قيم اليد
      const playerValue = calculateHandValue(playerHand);
      const dealerValue = calculateHandValue(dealerHand);
      
      // إنشاء حالة اللعبة
      const gameState = {
        deck,
        playerHand,
        dealerHand,
        amount,
        userId: interaction.user.id,
        isActive: true
      };
      
      // حفظ اللعبة في الذاكرة
      activeGames.set(interaction.user.id, gameState);
      
      // التحقق من بلاك جاك مباشر
      if (playerValue === 21) {
        // اللاعب لديه بلاك جاك
        const winAmount = Math.floor(amount * 2.5); // مكافأة البلاك جاك هي 2.5x
        userProfile.balance += winAmount;
        await userProfile.save();
        
        // تسجيل المعاملة
        logTransaction({
          type: 'blackjack',
          userId: interaction.user.id,
          username: interaction.user.username,
          amount: amount,
          result: 'blackjack',
          profit: winAmount - amount,
          timestamp: new Date()
        });
        
        // إنشاء رسالة النتيجة
        const embed = new EmbedBuilder()
          .setTitle('🃏 بلاك جاك')
          .setColor('#FFD700')
          .setDescription(`**بلاك جاك! 🎉**\nلقد حصلت على بلاك جاك مباشرة وربحت ${formatNumber(winAmount)} 💲!`)
          .addFields(
            { name: 'يدك', value: formatHand(playerHand) + ` (${playerValue})`, inline: true },
            { name: 'يد الموزع', value: formatHand(dealerHand) + ` (${dealerValue})`, inline: true },
            { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
          )
          .setFooter({ text: 'بلاك جاك! 🎉' })
          .setTimestamp();
        
        // إزالة اللعبة من الذاكرة
        activeGames.delete(interaction.user.id);
        
        return interaction.editReply({ embeds: [embed] });
      }
      
      // إنشاء أزرار التحكم
      const row = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('hit')
            .setLabel('سحب بطاقة')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('🎯'),
          new ButtonBuilder()
            .setCustomId('stand')
            .setLabel('الوقوف')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('🛑'),
          new ButtonBuilder()
            .setCustomId('surrender')
            .setLabel('استسلام')
            .setStyle(ButtonStyle.Danger)
            .setEmoji('🏳️')
        );
      
      // إنشاء رسالة اللعبة
      const embed = new EmbedBuilder()
        .setTitle('🃏 بلاك جاك')
        .setColor('#4169E1')
        .setDescription(`**مبلغ الرهان:** ${formatNumber(amount)} 💲`)
        .addFields(
          { name: 'يدك', value: formatHand(playerHand) + ` (${playerValue})`, inline: true },
          { name: 'يد الموزع', value: dealerHand[0].value + dealerHand[0].suit + ' ??' + ` (?)`, inline: true }
        )
        .setFooter({ text: 'اختر "سحب بطاقة" للحصول على بطاقة إضافية، أو "الوقوف" للاكتفاء بيدك الحالية' })
        .setTimestamp();
      
      // إرسال رسالة اللعبة مع الأزرار
      const response = await interaction.editReply({ embeds: [embed], components: [row] });
      
      // إنشاء مستمع للأزرار
      const collector = response.createMessageComponentCollector({ time: 60000 }); // مهلة 60 ثانية
      
      collector.on('collect', async (buttonInteraction) => {
        if (buttonInteraction.user.id !== interaction.user.id) {
          return buttonInteraction.reply({ content: '❌ هذه ليست لعبتك!', ephemeral: true });
        }
        
        // استرجاع حالة اللعبة
        const game = activeGames.get(interaction.user.id);
        
        if (!game || !game.isActive) {
          return buttonInteraction.reply({ content: '❌ هذه اللعبة قد انتهت بالفعل!', ephemeral: true });
        }
        
        switch (buttonInteraction.customId) {
          case 'hit':
            // سحب بطاقة إضافية
            game.playerHand.push(drawCard(game.deck));
            const newPlayerValue = calculateHandValue(game.playerHand);
            
            if (newPlayerValue > 21) {
              // اللاعب تجاوز 21 (خسر)
              game.isActive = false;
              
              // تسجيل المعاملة
              logTransaction({
                type: 'blackjack',
                userId: interaction.user.id,
                username: interaction.user.username,
                amount: amount,
                result: 'bust',
                profit: -amount,
                timestamp: new Date()
              });
              
              // إنشاء رسالة النتيجة
              const bustEmbed = new EmbedBuilder()
                .setTitle('🃏 بلاك جاك')
                .setColor('#F44336')
                .setDescription(`**تجاوزت الـ 21! ❌**\nلقد خسرت ${formatNumber(amount)} 💲.`)
                .addFields(
                  { name: 'يدك', value: formatHand(game.playerHand) + ` (${newPlayerValue})`, inline: true },
                  { name: 'يد الموزع', value: formatHand(game.dealerHand) + ` (${calculateHandValue(game.dealerHand)})`, inline: true },
                  { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
                )
                .setFooter({ text: 'حظًا أوفر في المرة القادمة!' })
                .setTimestamp();
              
              // إزالة اللعبة من الذاكرة
              activeGames.delete(interaction.user.id);
              
              await buttonInteraction.update({ embeds: [bustEmbed], components: [] });
            } else {
              // تحديث رسالة اللعبة
              const hitEmbed = new EmbedBuilder()
                .setTitle('🃏 بلاك جاك')
                .setColor('#4169E1')
                .setDescription(`**مبلغ الرهان:** ${formatNumber(amount)} 💲`)
                .addFields(
                  { name: 'يدك', value: formatHand(game.playerHand) + ` (${newPlayerValue})`, inline: true },
                  { name: 'يد الموزع', value: game.dealerHand[0].value + game.dealerHand[0].suit + ' ??' + ` (?)`, inline: true }
                )
                .setFooter({ text: 'اختر "سحب بطاقة" للحصول على بطاقة إضافية، أو "الوقوف" للاكتفاء بيدك الحالية' })
                .setTimestamp();
              
              await buttonInteraction.update({ embeds: [hitEmbed], components: [row] });
            }
            break;
            
          case 'stand':
            // اللاعب يقف، دور الموزع
            game.isActive = false;
            
            // الموزع يسحب بطاقات حتى يصل إلى 17 أو أكثر
            let dealerValue = calculateHandValue(game.dealerHand);
            
            while (dealerValue < 17) {
              game.dealerHand.push(drawCard(game.deck));
              dealerValue = calculateHandValue(game.dealerHand);
            }
            
            // تحديد الفائز
            const finalPlayerValue = calculateHandValue(game.playerHand);
            let result;
            let winAmount = 0;
            
            if (dealerValue > 21 || finalPlayerValue > dealerValue) {
              // اللاعب فاز
              winAmount = amount * 2;
              userProfile.balance += winAmount;
              result = 'win';
            } else if (finalPlayerValue === dealerValue) {
              // تعادل
              userProfile.balance += amount;
              result = 'push';
            } else {
              // اللاعب خسر (الموزع فاز)
              result = 'lose';
            }
            
            await userProfile.save();
            
            // تسجيل المعاملة
            logTransaction({
              type: 'blackjack',
              userId: interaction.user.id,
              username: interaction.user.username,
              amount: amount,
              result: result,
              profit: result === 'win' ? winAmount - amount : (result === 'push' ? 0 : -amount),
              timestamp: new Date()
            });
            
            // إنشاء رسالة النتيجة
            let resultText;
            let resultColor;
            
            if (result === 'win') {
              resultText = `**فزت! 🎉**\nلقد ربحت ${formatNumber(winAmount - amount)} 💲!`;
              resultColor = '#4CAF50';
            } else if (result === 'push') {
              resultText = '**تعادل! 🤝**\nتم إرجاع رهانك.';
              resultColor = '#FFC107';
            } else {
              resultText = `**خسرت! ❌**\nلقد خسرت ${formatNumber(amount)} 💲.`;
              resultColor = '#F44336';
            }
            
            const standEmbed = new EmbedBuilder()
              .setTitle('🃏 بلاك جاك')
              .setColor(resultColor)
              .setDescription(resultText)
              .addFields(
                { name: 'يدك', value: formatHand(game.playerHand) + ` (${finalPlayerValue})`, inline: true },
                { name: 'يد الموزع', value: formatHand(game.dealerHand) + ` (${dealerValue})`, inline: true },
                { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
              )
              .setFooter({ text: result === 'win' ? 'مبروك! 🎉' : (result === 'push' ? 'تعادل! 🤝' : 'حظًا أوفر في المرة القادمة!') })
              .setTimestamp();
            
            // إزالة اللعبة من الذاكرة
            activeGames.delete(interaction.user.id);
            
            await buttonInteraction.update({ embeds: [standEmbed], components: [] });
            break;
            
          case 'surrender':
            // اللاعب يستسلم، يسترد نصف المبلغ
            game.isActive = false;
            
            const refundAmount = Math.floor(amount / 2);
            userProfile.balance += refundAmount;
            await userProfile.save();
            
            // تسجيل المعاملة
            logTransaction({
              type: 'blackjack',
              userId: interaction.user.id,
              username: interaction.user.username,
              amount: amount,
              result: 'surrender',
              profit: -amount + refundAmount,
              timestamp: new Date()
            });
            
            // إنشاء رسالة النتيجة
            const surrenderEmbed = new EmbedBuilder()
              .setTitle('🃏 بلاك جاك')
              .setColor('#FFA500')
              .setDescription(`**استسلمت! 🏳️**\nتم إرجاع ${formatNumber(refundAmount)} 💲 (50% من رهانك).`)
              .addFields(
                { name: 'يدك', value: formatHand(game.playerHand) + ` (${calculateHandValue(game.playerHand)})`, inline: true },
                { name: 'يد الموزع', value: formatHand(game.dealerHand) + ` (${calculateHandValue(game.dealerHand)})`, inline: true },
                { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
              )
              .setFooter({ text: 'تم الاستسلام' })
              .setTimestamp();
            
            // إزالة اللعبة من الذاكرة
            activeGames.delete(interaction.user.id);
            
            await buttonInteraction.update({ embeds: [surrenderEmbed], components: [] });
            break;
        }
      });
      
      collector.on('end', async (collected, reason) => {
        if (reason === 'time') {
          // انتهت مهلة اللعبة
          const game = activeGames.get(interaction.user.id);
          
          if (game && game.isActive) {
            game.isActive = false;
            
            // إعادة نصف المبلغ
            const refundAmount = Math.floor(game.amount / 2);
            userProfile.balance += refundAmount;
            await userProfile.save();
            
            // تسجيل المعاملة
            logTransaction({
              type: 'blackjack',
              userId: interaction.user.id,
              username: interaction.user.username,
              amount: game.amount,
              result: 'timeout',
              profit: -game.amount + refundAmount,
              timestamp: new Date()
            });
            
            // إنشاء رسالة النتيجة
            const timeoutEmbed = new EmbedBuilder()
              .setTitle('🃏 بلاك جاك')
              .setColor('#808080')
              .setDescription(`**انتهت المهلة! ⏰**\nتم إرجاع ${formatNumber(refundAmount)} 💲 (50% من رهانك).`)
              .addFields(
                { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
              )
              .setFooter({ text: 'انتهت مهلة اللعبة' })
              .setTimestamp();
            
            // إزالة اللعبة من الذاكرة
            activeGames.delete(interaction.user.id);
            
            await interaction.editReply({ embeds: [timeoutEmbed], components: [] });
          }
        }
      });
    } catch (error) {
      console.error('خطأ في أمر البلاك جاك:', error);
      await interaction.editReply('حدث خطأ أثناء تنفيذ لعبة البلاك جاك. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
