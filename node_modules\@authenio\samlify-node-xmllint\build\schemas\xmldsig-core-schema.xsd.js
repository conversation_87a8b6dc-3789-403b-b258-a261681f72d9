"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<!DOCTYPE schema SYSTEM \"XMLSchema.dtd\"\n [\n   <!ATTLIST schema \n     xmlns:ds CDATA #FIXED \"http://www.w3.org/2000/09/xmldsig#\">\n   <!ENTITY dsig 'http://www.w3.org/2000/09/xmldsig#'>\n   <!ENTITY % p ''>\n   <!ENTITY % s ''>\n  ]>\n\n<!-- Schema for XML Signatures\n    http://www.w3.org/2000/09/xmldsig#\n    $Revision: 1.1 $ on $Date: 2002/02/08 20:32:26 $ by $Author: reagle $\n\n    Copyright 2001 The Internet Society and W3C (Massachusetts Institute\n    of Technology, Institut National de Recherche en Informatique et en\n    Automatique, Keio University). All Rights Reserved.\n    http://www.w3.org/Consortium/Legal/\n\n    This document is governed by the W3C Software License [1] as described\n    in the FAQ [2].\n\n    [1] http://www.w3.org/Consortium/Legal/copyright-software-19980720\n    [2] http://www.w3.org/Consortium/Legal/IPR-FAQ-20000620.html#DTD\n-->\n\n\n<schema xmlns=\"http://www.w3.org/2001/XMLSchema\"\n        xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\"\n        targetNamespace=\"http://www.w3.org/2000/09/xmldsig#\"\n        version=\"0.1\" elementFormDefault=\"qualified\"> \n\n<!-- Basic Types Defined for Signatures -->\n\n<simpleType name=\"CryptoBinary\">\n  <restriction base=\"base64Binary\">\n  </restriction>\n</simpleType>\n\n<!-- Start Signature -->\n\n<element name=\"Signature\" type=\"ds:SignatureType\"/>\n<complexType name=\"SignatureType\">\n  <sequence> \n    <element ref=\"ds:SignedInfo\"/> \n    <element ref=\"ds:SignatureValue\"/> \n    <element ref=\"ds:KeyInfo\" minOccurs=\"0\"/> \n    <element ref=\"ds:Object\" minOccurs=\"0\" maxOccurs=\"unbounded\"/> \n  </sequence>  \n  <attribute name=\"Id\" type=\"ID\" use=\"optional\"/>\n</complexType>\n\n  <element name=\"SignatureValue\" type=\"ds:SignatureValueType\"/> \n  <complexType name=\"SignatureValueType\">\n    <simpleContent>\n      <extension base=\"base64Binary\">\n        <attribute name=\"Id\" type=\"ID\" use=\"optional\"/>\n      </extension>\n    </simpleContent>\n  </complexType>\n\n<!-- Start SignedInfo -->\n\n<element name=\"SignedInfo\" type=\"ds:SignedInfoType\"/>\n<complexType name=\"SignedInfoType\">\n  <sequence> \n    <element ref=\"ds:CanonicalizationMethod\"/> \n    <element ref=\"ds:SignatureMethod\"/> \n    <element ref=\"ds:Reference\" maxOccurs=\"unbounded\"/> \n  </sequence>  \n  <attribute name=\"Id\" type=\"ID\" use=\"optional\"/> \n</complexType>\n\n  <element name=\"CanonicalizationMethod\" type=\"ds:CanonicalizationMethodType\"/> \n  <complexType name=\"CanonicalizationMethodType\" mixed=\"true\">\n    <sequence>\n      <any namespace=\"##any\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n      <!-- (0,unbounded) elements from (1,1) namespace -->\n    </sequence>\n    <attribute name=\"Algorithm\" type=\"anyURI\" use=\"required\"/> \n  </complexType>\n\n  <element name=\"SignatureMethod\" type=\"ds:SignatureMethodType\"/>\n  <complexType name=\"SignatureMethodType\" mixed=\"true\">\n    <sequence>\n      <element name=\"HMACOutputLength\" minOccurs=\"0\" type=\"ds:HMACOutputLengthType\"/>\n      <any namespace=\"##other\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n      <!-- (0,unbounded) elements from (1,1) external namespace -->\n    </sequence>\n    <attribute name=\"Algorithm\" type=\"anyURI\" use=\"required\"/> \n  </complexType>\n\n<!-- Start Reference -->\n\n<element name=\"Reference\" type=\"ds:ReferenceType\"/>\n<complexType name=\"ReferenceType\">\n  <sequence> \n    <element ref=\"ds:Transforms\" minOccurs=\"0\"/> \n    <element ref=\"ds:DigestMethod\"/> \n    <element ref=\"ds:DigestValue\"/> \n  </sequence>\n  <attribute name=\"Id\" type=\"ID\" use=\"optional\"/> \n  <attribute name=\"URI\" type=\"anyURI\" use=\"optional\"/> \n  <attribute name=\"Type\" type=\"anyURI\" use=\"optional\"/> \n</complexType>\n\n  <element name=\"Transforms\" type=\"ds:TransformsType\"/>\n  <complexType name=\"TransformsType\">\n    <sequence>\n      <element ref=\"ds:Transform\" maxOccurs=\"unbounded\"/>  \n    </sequence>\n  </complexType>\n\n  <element name=\"Transform\" type=\"ds:TransformType\"/>\n  <complexType name=\"TransformType\" mixed=\"true\">\n    <choice minOccurs=\"0\" maxOccurs=\"unbounded\"> \n      <any namespace=\"##other\" processContents=\"lax\"/>\n      <!-- (1,1) elements from (0,unbounded) namespaces -->\n      <element name=\"XPath\" type=\"string\"/> \n    </choice>\n    <attribute name=\"Algorithm\" type=\"anyURI\" use=\"required\"/> \n  </complexType>\n\n<!-- End Reference -->\n\n<element name=\"DigestMethod\" type=\"ds:DigestMethodType\"/>\n<complexType name=\"DigestMethodType\" mixed=\"true\"> \n  <sequence>\n    <any namespace=\"##other\" processContents=\"lax\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n  </sequence>    \n  <attribute name=\"Algorithm\" type=\"anyURI\" use=\"required\"/> \n</complexType>\n\n<element name=\"DigestValue\" type=\"ds:DigestValueType\"/>\n<simpleType name=\"DigestValueType\">\n  <restriction base=\"base64Binary\"/>\n</simpleType>\n\n<!-- End SignedInfo -->\n\n<!-- Start KeyInfo -->\n\n<element name=\"KeyInfo\" type=\"ds:KeyInfoType\"/> \n<complexType name=\"KeyInfoType\" mixed=\"true\">\n  <choice maxOccurs=\"unbounded\">     \n    <element ref=\"ds:KeyName\"/> \n    <element ref=\"ds:KeyValue\"/> \n    <element ref=\"ds:RetrievalMethod\"/> \n    <element ref=\"ds:X509Data\"/> \n    <element ref=\"ds:PGPData\"/> \n    <element ref=\"ds:SPKIData\"/>\n    <element ref=\"ds:MgmtData\"/>\n    <any processContents=\"lax\" namespace=\"##other\"/>\n    <!-- (1,1) elements from (0,unbounded) namespaces -->\n  </choice>\n  <attribute name=\"Id\" type=\"ID\" use=\"optional\"/> \n</complexType>\n\n  <element name=\"KeyName\" type=\"string\"/>\n  <element name=\"MgmtData\" type=\"string\"/>\n\n  <element name=\"KeyValue\" type=\"ds:KeyValueType\"/> \n  <complexType name=\"KeyValueType\" mixed=\"true\">\n   <choice>\n     <element ref=\"ds:DSAKeyValue\"/>\n     <element ref=\"ds:RSAKeyValue\"/>\n     <any namespace=\"##other\" processContents=\"lax\"/>\n   </choice>\n  </complexType>\n\n  <element name=\"RetrievalMethod\" type=\"ds:RetrievalMethodType\"/> \n  <complexType name=\"RetrievalMethodType\">\n    <sequence>\n      <element ref=\"ds:Transforms\" minOccurs=\"0\"/> \n    </sequence>  \n    <attribute name=\"URI\" type=\"anyURI\"/>\n    <attribute name=\"Type\" type=\"anyURI\" use=\"optional\"/>\n  </complexType>\n\n<!-- Start X509Data -->\n\n<element name=\"X509Data\" type=\"ds:X509DataType\"/> \n<complexType name=\"X509DataType\">\n  <sequence maxOccurs=\"unbounded\">\n    <choice>\n      <element name=\"X509IssuerSerial\" type=\"ds:X509IssuerSerialType\"/>\n      <element name=\"X509SKI\" type=\"base64Binary\"/>\n      <element name=\"X509SubjectName\" type=\"string\"/>\n      <element name=\"X509Certificate\" type=\"base64Binary\"/>\n      <element name=\"X509CRL\" type=\"base64Binary\"/>\n      <any namespace=\"##other\" processContents=\"lax\"/>\n    </choice>\n  </sequence>\n</complexType>\n\n<complexType name=\"X509IssuerSerialType\"> \n  <sequence> \n    <element name=\"X509IssuerName\" type=\"string\"/> \n    <element name=\"X509SerialNumber\" type=\"string\"/> \n  </sequence>\n</complexType>\n\n<!-- End X509Data -->\n\n<!-- Begin PGPData -->\n\n<element name=\"PGPData\" type=\"ds:PGPDataType\"/> \n<complexType name=\"PGPDataType\"> \n  <choice>\n    <sequence>\n      <element name=\"PGPKeyID\" type=\"base64Binary\"/> \n      <element name=\"PGPKeyPacket\" type=\"base64Binary\" minOccurs=\"0\"/> \n      <any namespace=\"##other\" processContents=\"lax\" minOccurs=\"0\"\n       maxOccurs=\"unbounded\"/>\n    </sequence>\n    <sequence>\n      <element name=\"PGPKeyPacket\" type=\"base64Binary\"/> \n      <any namespace=\"##other\" processContents=\"lax\" minOccurs=\"0\"\n       maxOccurs=\"unbounded\"/>\n    </sequence>\n  </choice>\n</complexType>\n\n<!-- End PGPData -->\n\n<!-- Begin SPKIData -->\n\n<element name=\"SPKIData\" type=\"ds:SPKIDataType\"/> \n<complexType name=\"SPKIDataType\">\n  <sequence maxOccurs=\"unbounded\">\n    <element name=\"SPKISexp\" type=\"base64Binary\"/>\n    <any namespace=\"##other\" processContents=\"lax\" minOccurs=\"0\"/>\n  </sequence>\n</complexType> \n\n<!-- End SPKIData -->\n\n<!-- End KeyInfo -->\n\n<!-- Start Object (Manifest, SignatureProperty) -->\n\n<element name=\"Object\" type=\"ds:ObjectType\"/> \n<complexType name=\"ObjectType\" mixed=\"true\">\n  <sequence minOccurs=\"0\" maxOccurs=\"unbounded\">\n    <any namespace=\"##any\" processContents=\"lax\"/>\n  </sequence>\n  <attribute name=\"Id\" type=\"ID\" use=\"optional\"/> \n  <attribute name=\"MimeType\" type=\"string\" use=\"optional\"/> <!-- add a grep facet -->\n  <attribute name=\"Encoding\" type=\"anyURI\" use=\"optional\"/> \n</complexType>\n\n<element name=\"Manifest\" type=\"ds:ManifestType\"/> \n<complexType name=\"ManifestType\">\n  <sequence>\n    <element ref=\"ds:Reference\" maxOccurs=\"unbounded\"/> \n  </sequence>\n  <attribute name=\"Id\" type=\"ID\" use=\"optional\"/> \n</complexType>\n\n<element name=\"SignatureProperties\" type=\"ds:SignaturePropertiesType\"/> \n<complexType name=\"SignaturePropertiesType\">\n  <sequence>\n    <element ref=\"ds:SignatureProperty\" maxOccurs=\"unbounded\"/> \n  </sequence>\n  <attribute name=\"Id\" type=\"ID\" use=\"optional\"/> \n</complexType>\n\n   <element name=\"SignatureProperty\" type=\"ds:SignaturePropertyType\"/> \n   <complexType name=\"SignaturePropertyType\" mixed=\"true\">\n     <choice maxOccurs=\"unbounded\">\n       <any namespace=\"##other\" processContents=\"lax\"/>\n       <!-- (1,1) elements from (1,unbounded) namespaces -->\n     </choice>\n     <attribute name=\"Target\" type=\"anyURI\" use=\"required\"/> \n     <attribute name=\"Id\" type=\"ID\" use=\"optional\"/> \n   </complexType>\n\n<!-- End Object (Manifest, SignatureProperty) -->\n\n<!-- Start Algorithm Parameters -->\n\n<simpleType name=\"HMACOutputLengthType\">\n  <restriction base=\"integer\"/>\n</simpleType>\n\n<!-- Start KeyValue Element-types -->\n\n<element name=\"DSAKeyValue\" type=\"ds:DSAKeyValueType\"/>\n<complexType name=\"DSAKeyValueType\">\n  <sequence>\n    <sequence minOccurs=\"0\">\n      <element name=\"P\" type=\"ds:CryptoBinary\"/>\n      <element name=\"Q\" type=\"ds:CryptoBinary\"/>\n    </sequence>\n    <element name=\"G\" type=\"ds:CryptoBinary\" minOccurs=\"0\"/>\n    <element name=\"Y\" type=\"ds:CryptoBinary\"/>\n    <element name=\"J\" type=\"ds:CryptoBinary\" minOccurs=\"0\"/>\n    <sequence minOccurs=\"0\">\n      <element name=\"Seed\" type=\"ds:CryptoBinary\"/>\n      <element name=\"PgenCounter\" type=\"ds:CryptoBinary\"/>\n    </sequence>\n  </sequence>\n</complexType>\n\n<element name=\"RSAKeyValue\" type=\"ds:RSAKeyValueType\"/>\n<complexType name=\"RSAKeyValueType\">\n  <sequence>\n    <element name=\"Modulus\" type=\"ds:CryptoBinary\"/> \n    <element name=\"Exponent\" type=\"ds:CryptoBinary\"/> \n  </sequence>\n</complexType> \n\n<!-- End KeyValue Element-types -->\n\n<!-- End Signature -->\n\n</schema>";
//# sourceMappingURL=xmldsig-core-schema.xsd.js.map