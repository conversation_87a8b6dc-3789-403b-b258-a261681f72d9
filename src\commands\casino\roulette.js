const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('روليت')
    .setDescription('لعب الروليت بمبلغ معين')
    .addIntegerOption(option => 
      option.setName('المبلغ')
        .setDescription('المبلغ المراد المراهنة به')
        .setRequired(true)
        .setMinValue(100))
    .addStringOption(option => 
      option.setName('الرهان')
        .setDescription('نوع الرهان الخاص بك')
        .setRequired(true)
        .addChoices(
          { name: 'أحمر', value: 'red' },
          { name: 'أسود', value: 'black' },
          { name: 'فردي', value: 'odd' },
          { name: 'زوجي', value: 'even' },
          { name: '1-18', value: 'low' },
          { name: '19-36', value: 'high' }
        )),
  
  async execute(interaction) {
    await interaction.deferReply();
    
    const amount = interaction.options.getInteger('المبلغ');
    const betType = interaction.options.getString('الرهان');
    
    try {
      // البحث عن حساب المستخدم
      let userProfile = await User.findOne({ userId: interaction.user.id });
      
      if (!userProfile) {
        // إنشاء حساب جديد للمستخدم إذا لم يكن موجودًا
        userProfile = await User.create({
          userId: interaction.user.id,
          username: interaction.user.username,
          balance: 1000,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // التحقق من وجود رصيد كافٍ
      if (userProfile.balance < amount) {
        return interaction.editReply(`❌ ليس لديك رصيد كافٍ! رصيدك الحالي هو ${formatNumber(userProfile.balance)} 💲`);
      }
      
      // تنفيذ لعبة الروليت
      const number = Math.floor(Math.random() * 37); // 0-36
      const isRed = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36].includes(number);
      const isBlack = !isRed && number !== 0;
      const isOdd = number !== 0 && number % 2 === 1;
      const isEven = number !== 0 && number % 2 === 0;
      const isLow = number >= 1 && number <= 18;
      const isHigh = number >= 19 && number <= 36;
      
      let won = false;
      let multiplier = 1;
      
      // تحديد الفائز بناءً على نوع الرهان
      switch (betType) {
        case 'red':
          won = isRed;
          multiplier = 2;
          break;
        case 'black':
          won = isBlack;
          multiplier = 2;
          break;
        case 'odd':
          won = isOdd;
          multiplier = 2;
          break;
        case 'even':
          won = isEven;
          multiplier = 2;
          break;
        case 'low':
          won = isLow;
          multiplier = 2;
          break;
        case 'high':
          won = isHigh;
          multiplier = 2;
          break;
      }
      
      // تحديث رصيد المستخدم
      if (won) {
        userProfile.balance += amount * multiplier;
      } else {
        userProfile.balance -= amount;
      }
      
      // حفظ التغييرات
      await userProfile.save();
      
      // تسجيل المعاملة
      logTransaction({
        type: 'roulette',
        userId: interaction.user.id,
        username: interaction.user.username,
        amount: amount,
        betType: betType,
        result: won ? 'win' : 'lose',
        profit: won ? amount * multiplier : -amount,
        timestamp: new Date()
      });
      
      // تحديد لون النتيجة
      let resultColor;
      if (number === 0) {
        resultColor = '🟢';
      } else if (isRed) {
        resultColor = '🔴';
      } else {
        resultColor = '⚫';
      }
      
      // إنشاء رسالة النتيجة
      const embed = new EmbedBuilder()
        .setTitle('🎰 روليت الكازينو')
        .setColor(won ? '#4CAF50' : '#F44336')
        .setDescription(`الكرة استقرت على: ${resultColor} **${number}**`)
        .addFields(
          { name: 'نوع الرهان', value: betType, inline: true },
          { name: 'النتيجة', value: won ? '🎉 فوز!' : '❌ خسارة', inline: true },
          { name: won ? 'المبلغ المربوح' : 'المبلغ المخسور', value: `${formatNumber(won ? amount * multiplier : amount)} 💲`, inline: true },
          { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
        )
        .setFooter({ text: `معرف اللعبة: ${Date.now().toString(36).toUpperCase()}` })
        .setTimestamp();
      
      await interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error('خطأ في أمر الروليت:', error);
      await interaction.editReply('حدث خطأ أثناء تنفيذ لعبة الروليت. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
