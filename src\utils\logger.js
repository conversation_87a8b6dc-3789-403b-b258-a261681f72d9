const Transaction = require('../models/Transaction');

/**
 * تسجيل معاملة في قاعدة البيانات
 * @param {Object} transactionData بيانات المعاملة
 */
async function logTransaction(transactionData) {
  try {
    // إضافة الطابع الزمني إذا لم يكن موجودًا
    if (!transactionData.timestamp) {
      transactionData.timestamp = new Date();
    }
    
    // إنشاء معاملة جديدة
    const transaction = new Transaction(transactionData);
    
    // حفظ المعاملة
    await transaction.save();
    
    console.log(`📝 تم تسجيل معاملة جديدة من النوع: ${transactionData.type}`);
  } catch (error) {
    console.error('❌ خطأ في تسجيل المعاملة:', error);
  }
}

/**
 * الحصول على معاملات المستخدم
 * @param {string} userId معرف المستخدم
 * @param {number} limit عدد المعاملات للجلب (الافتراضي: 10)
 * @returns {Promise<Array>} قائمة المعاملات
 */
async function getUserTransactions(userId, limit = 10) {
  try {
    const transactions = await Transaction.find({ userId })
      .sort({ timestamp: -1 })
      .limit(limit);
    
    return transactions;
  } catch (error) {
    console.error('❌ خطأ في جلب معاملات المستخدم:', error);
    return [];
  }
}

module.exports = {
  logTransaction,
  getUserTransactions
};
