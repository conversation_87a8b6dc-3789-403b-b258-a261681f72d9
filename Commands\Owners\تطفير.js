const fs = require('fs');
const { MessageEmbed } = require('discord.js');
const Data = require('pro.db');
const { owners } = require(`${process.cwd()}/config`);

module.exports = {
    name: "تطفير",
    description: "تصفير جميع حسابات البنك",
    run: async (client, message, args) => {
        if (!owners.includes(message.author.id)) return message.react('❌');

        fs.readFile('database.json', 'utf8', async (err, data) => {
            if (err) {
                console.error(err);
                return message.reply("حدث خطأ أثناء قراءة قاعدة البيانات.");
            }

            const database = JSON.parse(data);
            const keysToReset = ["money_", "marriage_", "protectionHours_", "totalLoot_" ,"owned_" ];
            const usersWithBalance = Object.keys(database)
                .filter(key => keysToReset.some(prefix => key.startsWith(prefix)) && database[key] > 0);
            
            usersWithBalance.forEach(userId => {
                keysToReset.forEach(prefix => {
                    const key = userId.replace(/(money_|marriage_|protectionHours_|totalLoot_|owned_)/, prefix);
                    if (database[key] !== undefined) {
                        database[key] = 0; // Reset the value to 0
                    }
                });
            });

            fs.writeFile('database.json', JSON.stringify(database, null, 2), 'utf8', async (err) => {
                if (err) {
                    console.error(err);
                    return message.reply("حدث خطأ أثناء كتابة قاعدة البيانات.");
                }

                message.reply("تم تصفير جميع الحسابات.");
                message.react("☑️");

                // تسجيل اللوج
                const logChannelId = await Data.get(`logChannel_${message.guild.id}`);
                const logChannel = message.guild.channels.cache.get(logChannelId);
                if (logChannel) {
                    const logEmbed = new MessageEmbed()
                        .setTitle("تطفير الحسابات")
                        .setDescription(`**تم تصفير جميع الحسابات في البنك من قبل <@${message.author.id}>**`)
                        .addField("الاداري", `<@${message.author.id}>`)
                        .setThumbnail('https://e.top4top.io/p_31278dayn1.png')
                        .setColor("#000")
                        .setFooter({ text: 'Sky Store', iconURL: 'https://k.top4top.io/p_3299aejba1.png' });
                
                    logChannel.send({ embeds: [logEmbed] });
                }                
            });
        });
    }
};
