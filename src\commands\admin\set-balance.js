const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('تعديل_رصيد')
    .setDescription('تعديل رصيد مستخدم (للمسؤولين فقط)')
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
    .addUserOption(option => 
      option.setName('مستخدم')
        .setDescription('المستخدم المراد تعديل رصيده')
        .setRequired(true))
    .addStringOption(option => 
      option.setName('نوع')
        .setDescription('نوع تعديل الرصيد')
        .setRequired(true)
        .addChoices(
          { name: 'الرصيد النقدي', value: 'balance' },
          { name: 'الرصيد البنكي', value: 'bank' }
        ))
    .addStringOption(option => 
      option.setName('عملية')
        .setDescription('نوع العملية')
        .setRequired(true)
        .addChoices(
          { name: 'تعيين', value: 'set' },
          { name: 'إضافة', value: 'add' },
          { name: 'طرح', value: 'subtract' }
        ))
    .addIntegerOption(option => 
      option.setName('المبلغ')
        .setDescription('المبلغ')
        .setRequired(true)
        .setMinValue(0)),
  
  async execute(interaction) {
    await interaction.deferReply({ ephemeral: true });
    
    const targetUser = interaction.options.getUser('مستخدم');
    const balanceType = interaction.options.getString('نوع');
    const operationType = interaction.options.getString('عملية');
    const amount = interaction.options.getInteger('المبلغ');
    
    try {
      // البحث عن حساب المستخدم
      let userProfile = await User.findOne({ userId: targetUser.id });
      
      if (!userProfile) {
        // إنشاء حساب جديد للمستخدم إذا لم يكن موجودًا
        userProfile = await User.create({
          userId: targetUser.id,
          username: targetUser.username,
          balance: 0,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // القيمة القديمة قبل التعديل
      const oldValue = balanceType === 'balance' ? userProfile.balance : userProfile.bank;
      
      // تنفيذ العملية
      switch (operationType) {
        case 'set':
          if (balanceType === 'balance') {
            userProfile.balance = amount;
          } else {
            userProfile.bank = amount;
          }
          break;
        case 'add':
          if (balanceType === 'balance') {
            userProfile.balance += amount;
          } else {
            userProfile.bank += amount;
          }
          break;
        case 'subtract':
          if (balanceType === 'balance') {
            userProfile.balance = Math.max(0, userProfile.balance - amount);
          } else {
            userProfile.bank = Math.max(0, userProfile.bank - amount);
          }
          break;
      }
      
      // حفظ التغييرات
      await userProfile.save();
      
      // القيمة الجديدة بعد التعديل
      const newValue = balanceType === 'balance' ? userProfile.balance : userProfile.bank;
      
      // تسجيل المعاملة
      logTransaction({
        type: 'admin_balance_update',
        adminId: interaction.user.id,
        adminName: interaction.user.username,
        targetId: targetUser.id,
        targetName: targetUser.username,
        balanceType: balanceType,
        operation: operationType,
        amount: amount,
        oldValue: oldValue,
        newValue: newValue,
        timestamp: new Date()
      });
      
      // إنشاء رسالة التأكيد
      const embed = new EmbedBuilder()
        .setTitle('💼 تعديل الرصيد')
        .setColor('#9C27B0')
        .setDescription(`تم تعديل رصيد ${targetUser.username} بنجاح!`)
        .addFields(
          { name: 'نوع الرصيد', value: balanceType === 'balance' ? 'الرصيد النقدي' : 'الرصيد البنكي', inline: true },
          { name: 'العملية', value: operationType === 'set' ? 'تعيين' : (operationType === 'add' ? 'إضافة' : 'طرح'), inline: true },
          { name: 'المبلغ', value: `${formatNumber(amount)} 💲`, inline: true },
          { name: 'القيمة السابقة', value: `${formatNumber(oldValue)} 💲`, inline: true },
          { name: 'القيمة الجديدة', value: `${formatNumber(newValue)} 💲`, inline: true }
        )
        .setFooter({ text: `معرف المعاملة: ${Date.now().toString(36).toUpperCase()}` })
        .setTimestamp();
      
      await interaction.editReply({ embeds: [embed] });
      
      // إرسال إشعار للمستخدم المستهدف
      try {
        const userEmbed = new EmbedBuilder()
          .setTitle('💰 إشعار تعديل الرصيد')
          .setColor('#9C27B0')
          .setDescription(`تم تعديل رصيدك بواسطة ${interaction.user.username}!`)
          .addFields(
            { name: 'نوع الرصيد', value: balanceType === 'balance' ? 'الرصيد النقدي' : 'الرصيد البنكي', inline: true },
            { name: 'العملية', value: operationType === 'set' ? 'تعيين' : (operationType === 'add' ? 'إضافة' : 'طرح'), inline: true },
            { name: 'المبلغ', value: `${formatNumber(amount)} 💲`, inline: true },
            { name: 'رصيدك الحالي', value: `${formatNumber(balanceType === 'balance' ? userProfile.balance : userProfile.bank)} 💲`, inline: true }
          )
          .setTimestamp();
        
        await targetUser.send({ embeds: [userEmbed] });
      } catch (error) {
        console.log(`لم يتمكن من إرسال إشعار للمستخدم ${targetUser.username}: ${error.message}`);
      }
    } catch (error) {
      console.error('خطأ في أمر تعديل الرصيد:', error);
      await interaction.editReply('حدث خطأ أثناء تعديل الرصيد. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
