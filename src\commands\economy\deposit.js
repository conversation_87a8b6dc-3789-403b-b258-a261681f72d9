const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('إيداع')
    .setDescription('إيداع الأموال في البنك')
    .addStringOption(option => 
      option.setName('المبلغ')
        .setDescription('المبلغ المراد إيداعه (يمكنك كتابة "الكل" لإيداع كل رصيدك)')
        .setRequired(true)),
  
  async execute(interaction) {
    await interaction.deferReply();
    
    const amountInput = interaction.options.getString('المبلغ');
    let amount;
    
    try {
      // البحث عن حساب المستخدم
      let userProfile = await User.findOne({ userId: interaction.user.id });
      
      if (!userProfile) {
        // إنشاء حساب جديد للمستخدم إذا لم يكن موجودًا
        userProfile = await User.create({
          userId: interaction.user.id,
          username: interaction.user.username,
          balance: 1000,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // التحقق من المبلغ المدخل
      if (amountInput.toLowerCase() === 'الكل' || amountInput.toLowerCase() === 'all') {
        amount = userProfile.balance;
      } else {
        amount = parseInt(amountInput);
        
        if (isNaN(amount) || amount <= 0) {
          return interaction.editReply('❌ يرجى إدخال مبلغ صحيح أكبر من صفر أو كتابة "الكل".');
        }
      }
      
      // التحقق من وجود رصيد كافٍ
      if (userProfile.balance < amount) {
        return interaction.editReply(`❌ ليس لديك رصيد كافٍ! رصيدك الحالي هو ${formatNumber(userProfile.balance)} 💲`);
      }
      
      // إجراء عملية الإيداع
      userProfile.balance -= amount;
      userProfile.bank += amount;
      
      // حفظ التغييرات
      await userProfile.save();
      
      // تسجيل المعاملة
      logTransaction({
        type: 'deposit',
        userId: interaction.user.id,
        username: interaction.user.username,
        amount: amount,
        timestamp: new Date()
      });
      
      // إنشاء رسالة تأكيد
      const embed = new EmbedBuilder()
        .setTitle('🏦 إيداع ناجح')
        .setColor('#4CAF50')
        .setDescription(`تم إيداع ${formatNumber(amount)} 💲 في حسابك المصرفي بنجاح!`)
        .addFields(
          { name: '💵 النقود في اليد', value: `${formatNumber(userProfile.balance)} 💲`, inline: true },
          { name: '🏦 النقود في البنك', value: `${formatNumber(userProfile.bank)} 💲`, inline: true }
        )
        .setFooter({ text: `معرف المعاملة: ${Date.now().toString(36).toUpperCase()}` })
        .setTimestamp();
      
      await interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error('خطأ في أمر الإيداع:', error);
      await interaction.editReply('حدث خطأ أثناء تنفيذ عملية الإيداع. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
