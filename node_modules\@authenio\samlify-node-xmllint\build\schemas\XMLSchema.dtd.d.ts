declare const _default: "<!-- DTD for XML Schemas: Part 1: Structures\n     Public Identifier: \"-//W3C//DTD XMLSCHEMA 200102//EN\"\n     Official Location: http://www.w3.org/2001/XMLSchema.dtd -->\n<!-- $Id: XMLSchema.dtd,v 1.31 2001/10/24 15:50:16 ht Exp $ -->\n<!-- Note this DTD is NOT normative, or even definitive. -->           <!--d-->\n<!-- prose copy in the structures REC is the definitive version -->    <!--d-->\n<!-- (which shouldn't differ from this one except for this -->         <!--d-->\n<!-- comment and entity expansions, but just in case) -->              <!--d-->\n<!-- With the exception of cases with multiple namespace\n     prefixes for the XML Schema namespace, any XML document which is\n     not valid per this DTD given redefinitions in its internal subset of the\n     'p' and 's' parameter entities below appropriate to its namespace\n     declaration of the XML Schema namespace is almost certainly not\n     a valid schema. -->\n\n<!-- The simpleType element and its constituent parts\n     are defined in XML Schema: Part 2: Datatypes -->\n<!ENTITY % xs-datatypes PUBLIC 'datatypes' 'datatypes.dtd' >\n\n<!ENTITY % p 'xs:'> <!-- can be overriden in the internal subset of a\n                         schema document to establish a different\n                         namespace prefix -->\n<!ENTITY % s ':xs'> <!-- if %p is defined (e.g. as foo:) then you must\n                         also define %s as the suffix for the appropriate\n                         namespace declaration (e.g. :foo) -->\n<!ENTITY % nds 'xmlns%s;'>\n\n<!-- Define all the element names, with optional prefix -->\n<!ENTITY % schema \"%p;schema\">\n<!ENTITY % complexType \"%p;complexType\">\n<!ENTITY % complexContent \"%p;complexContent\">\n<!ENTITY % simpleContent \"%p;simpleContent\">\n<!ENTITY % extension \"%p;extension\">\n<!ENTITY % element \"%p;element\">\n<!ENTITY % unique \"%p;unique\">\n<!ENTITY % key \"%p;key\">\n<!ENTITY % keyref \"%p;keyref\">\n<!ENTITY % selector \"%p;selector\">\n<!ENTITY % field \"%p;field\">\n<!ENTITY % group \"%p;group\">\n<!ENTITY % all \"%p;all\">\n<!ENTITY % choice \"%p;choice\">\n<!ENTITY % sequence \"%p;sequence\">\n<!ENTITY % any \"%p;any\">\n<!ENTITY % anyAttribute \"%p;anyAttribute\">\n<!ENTITY % attribute \"%p;attribute\">\n<!ENTITY % attributeGroup \"%p;attributeGroup\">\n<!ENTITY % include \"%p;include\">\n<!ENTITY % import \"%p;import\">\n<!ENTITY % redefine \"%p;redefine\">\n<!ENTITY % notation \"%p;notation\">\n\n<!-- annotation elements -->\n<!ENTITY % annotation \"%p;annotation\">\n<!ENTITY % appinfo \"%p;appinfo\">\n<!ENTITY % documentation \"%p;documentation\">\n\n<!-- Customisation entities for the ATTLIST of each element type.\n     Define one of these if your schema takes advantage of the\n     anyAttribute='##other' in the schema for schemas -->\n\n<!ENTITY % schemaAttrs ''>\n<!ENTITY % complexTypeAttrs ''>\n<!ENTITY % complexContentAttrs ''>\n<!ENTITY % simpleContentAttrs ''>\n<!ENTITY % extensionAttrs ''>\n<!ENTITY % elementAttrs ''>\n<!ENTITY % groupAttrs ''>\n<!ENTITY % allAttrs ''>\n<!ENTITY % choiceAttrs ''>\n<!ENTITY % sequenceAttrs ''>\n<!ENTITY % anyAttrs ''>\n<!ENTITY % anyAttributeAttrs ''>\n<!ENTITY % attributeAttrs ''>\n<!ENTITY % attributeGroupAttrs ''>\n<!ENTITY % uniqueAttrs ''>\n<!ENTITY % keyAttrs ''>\n<!ENTITY % keyrefAttrs ''>\n<!ENTITY % selectorAttrs ''>\n<!ENTITY % fieldAttrs ''>\n<!ENTITY % includeAttrs ''>\n<!ENTITY % importAttrs ''>\n<!ENTITY % redefineAttrs ''>\n<!ENTITY % notationAttrs ''>\n<!ENTITY % annotationAttrs ''>\n<!ENTITY % appinfoAttrs ''>\n<!ENTITY % documentationAttrs ''>\n\n<!ENTITY % complexDerivationSet \"CDATA\">\n      <!-- #all or space-separated list drawn from derivationChoice -->\n<!ENTITY % blockSet \"CDATA\">\n      <!-- #all or space-separated list drawn from\n                      derivationChoice + 'substitution' -->\n\n<!ENTITY % mgs '%all; | %choice; | %sequence;'>\n<!ENTITY % cs '%choice; | %sequence;'>\n<!ENTITY % formValues '(qualified|unqualified)'>\n\n\n<!ENTITY % attrDecls    '((%attribute;| %attributeGroup;)*,(%anyAttribute;)?)'>\n\n<!ENTITY % particleAndAttrs '((%mgs; | %group;)?, %attrDecls;)'>\n\n<!-- This is used in part2 -->\n<!ENTITY % restriction1 '((%mgs; | %group;)?)'>\n\n%xs-datatypes;\n\n<!-- the duplication below is to produce an unambiguous content model\n     which allows annotation everywhere -->\n<!ELEMENT %schema; ((%include; | %import; | %redefine; | %annotation;)*,\n                    ((%simpleType; | %complexType;\n                      | %element; | %attribute;\n                      | %attributeGroup; | %group;\n                      | %notation; ),\n                     (%annotation;)*)* )>\n<!ATTLIST %schema;\n   targetNamespace      %URIref;               #IMPLIED\n   version              CDATA                  #IMPLIED\n   %nds;                %URIref;               #FIXED 'http://www.w3.org/2001/XMLSchema'\n   xmlns                CDATA                  #IMPLIED\n   finalDefault         %complexDerivationSet; ''\n   blockDefault         %blockSet;             ''\n   id                   ID                     #IMPLIED\n   elementFormDefault   %formValues;           'unqualified'\n   attributeFormDefault %formValues;           'unqualified'\n   xml:lang             CDATA                  #IMPLIED\n   %schemaAttrs;>\n<!-- Note the xmlns declaration is NOT in the Schema for Schemas,\n     because at the Infoset level where schemas operate,\n     xmlns(:prefix) is NOT an attribute! -->\n<!-- The declaration of xmlns is a convenience for schema authors -->\n \n<!-- The id attribute here and below is for use in external references\n     from non-schemas using simple fragment identifiers.\n     It is NOT used for schema-to-schema reference, internal or\n     external. -->\n\n<!-- a type is a named content type specification which allows attribute\n     declarations-->\n<!-- -->\n\n<!ELEMENT %complexType; ((%annotation;)?,\n                         (%simpleContent;|%complexContent;|\n                          %particleAndAttrs;))>\n\n<!ATTLIST %complexType;\n          name      %NCName;                        #IMPLIED\n          id        ID                              #IMPLIED\n          abstract  %boolean;                       #IMPLIED\n          final     %complexDerivationSet;          #IMPLIED\n          block     %complexDerivationSet;          #IMPLIED\n          mixed (true|false) 'false'\n          %complexTypeAttrs;>\n\n<!-- particleAndAttrs is shorthand for a root type -->\n<!-- mixed is disallowed if simpleContent, overriden if complexContent\n     has one too. -->\n\n<!-- If anyAttribute appears in one or more referenced attributeGroups\n     and/or explicitly, the intersection of the permissions is used -->\n\n<!ELEMENT %complexContent; ((%annotation;)?, (%restriction;|%extension;))>\n<!ATTLIST %complexContent;\n          mixed (true|false) #IMPLIED\n          id    ID           #IMPLIED\n          %complexContentAttrs;>\n\n<!-- restriction should use the branch defined above, not the simple\n     one from part2; extension should use the full model  -->\n\n<!ELEMENT %simpleContent; ((%annotation;)?, (%restriction;|%extension;))>\n<!ATTLIST %simpleContent;\n          id    ID           #IMPLIED\n          %simpleContentAttrs;>\n\n<!-- restriction should use the simple branch from part2, not the \n     one defined above; extension should have no particle  -->\n\n<!ELEMENT %extension; ((%annotation;)?, (%particleAndAttrs;))>\n<!ATTLIST %extension;\n          base  %QName;      #REQUIRED\n          id    ID           #IMPLIED\n          %extensionAttrs;>\n\n<!-- an element is declared by either:\n a name and a type (either nested or referenced via the type attribute)\n or a ref to an existing element declaration -->\n\n<!ELEMENT %element; ((%annotation;)?, (%complexType;| %simpleType;)?,\n                     (%unique; | %key; | %keyref;)*)>\n<!-- simpleType or complexType only if no type|ref attribute -->\n<!-- ref not allowed at top level -->\n<!ATTLIST %element;\n            name               %NCName;               #IMPLIED\n            id                 ID                     #IMPLIED\n            ref                %QName;                #IMPLIED\n            type               %QName;                #IMPLIED\n            minOccurs          %nonNegativeInteger;   #IMPLIED\n            maxOccurs          CDATA                  #IMPLIED\n            nillable           %boolean;              #IMPLIED\n            substitutionGroup  %QName;                #IMPLIED\n            abstract           %boolean;              #IMPLIED\n            final              %complexDerivationSet; #IMPLIED\n            block              %blockSet;             #IMPLIED\n            default            CDATA                  #IMPLIED\n            fixed              CDATA                  #IMPLIED\n            form               %formValues;           #IMPLIED\n            %elementAttrs;>\n<!-- type and ref are mutually exclusive.\n     name and ref are mutually exclusive, one is required -->\n<!-- In the absence of type AND ref, type defaults to type of\n     substitutionGroup, if any, else the ur-type, i.e. unconstrained -->\n<!-- default and fixed are mutually exclusive -->\n\n<!ELEMENT %group; ((%annotation;)?,(%mgs;)?)>\n<!ATTLIST %group; \n          name        %NCName;               #IMPLIED\n          ref         %QName;                #IMPLIED\n          minOccurs   %nonNegativeInteger;   #IMPLIED\n          maxOccurs   CDATA                  #IMPLIED\n          id          ID                     #IMPLIED\n          %groupAttrs;>\n\n<!ELEMENT %all; ((%annotation;)?, (%element;)*)>\n<!ATTLIST %all;\n          minOccurs   (1)                    #IMPLIED\n          maxOccurs   (1)                    #IMPLIED\n          id          ID                     #IMPLIED\n          %allAttrs;>\n\n<!ELEMENT %choice; ((%annotation;)?, (%element;| %group;| %cs; | %any;)*)>\n<!ATTLIST %choice;\n          minOccurs   %nonNegativeInteger;   #IMPLIED\n          maxOccurs   CDATA                  #IMPLIED\n          id          ID                     #IMPLIED\n          %choiceAttrs;>\n\n<!ELEMENT %sequence; ((%annotation;)?, (%element;| %group;| %cs; | %any;)*)>\n<!ATTLIST %sequence;\n          minOccurs   %nonNegativeInteger;   #IMPLIED\n          maxOccurs   CDATA                  #IMPLIED\n          id          ID                     #IMPLIED\n          %sequenceAttrs;>\n\n<!-- an anonymous grouping in a model, or\n     a top-level named group definition, or a reference to same -->\n\n<!-- Note that if order is 'all', group is not allowed inside.\n     If order is 'all' THIS group must be alone (or referenced alone) at\n     the top level of a content model -->\n<!-- If order is 'all', minOccurs==maxOccurs==1 on element/any inside -->\n<!-- Should allow minOccurs=0 inside order='all' . . . -->\n\n<!ELEMENT %any; (%annotation;)?>\n<!ATTLIST %any;\n            namespace       CDATA                  '##any'\n            processContents (skip|lax|strict)      'strict'\n            minOccurs       %nonNegativeInteger;   '1'\n            maxOccurs       CDATA                  '1'\n            id              ID                     #IMPLIED\n            %anyAttrs;>\n\n<!-- namespace is interpreted as follows:\n                  ##any      - - any non-conflicting WFXML at all\n\n                  ##other    - - any non-conflicting WFXML from namespace other\n                                  than targetNamespace\n\n                  ##local    - - any unqualified non-conflicting WFXML/attribute\n                  one or     - - any non-conflicting WFXML from\n                  more URI        the listed namespaces\n                  references\n\n                  ##targetNamespace ##local may appear in the above list,\n                    with the obvious meaning -->\n\n<!ELEMENT %anyAttribute; (%annotation;)?>\n<!ATTLIST %anyAttribute;\n            namespace       CDATA              '##any'\n            processContents (skip|lax|strict)  'strict'\n            id              ID                 #IMPLIED\n            %anyAttributeAttrs;>\n<!-- namespace is interpreted as for 'any' above -->\n\n<!-- simpleType only if no type|ref attribute -->\n<!-- ref not allowed at top level, name iff at top level -->\n<!ELEMENT %attribute; ((%annotation;)?, (%simpleType;)?)>\n<!ATTLIST %attribute;\n          name      %NCName;      #IMPLIED\n          id        ID            #IMPLIED\n          ref       %QName;       #IMPLIED\n          type      %QName;       #IMPLIED\n          use       (prohibited|optional|required) #IMPLIED\n          default   CDATA         #IMPLIED\n          fixed     CDATA         #IMPLIED\n          form      %formValues;  #IMPLIED\n          %attributeAttrs;>\n<!-- type and ref are mutually exclusive.\n     name and ref are mutually exclusive, one is required -->\n<!-- default for use is optional when nested, none otherwise -->\n<!-- default and fixed are mutually exclusive -->\n<!-- type attr and simpleType content are mutually exclusive -->\n\n<!-- an attributeGroup is a named collection of attribute decls, or a\n     reference thereto -->\n<!ELEMENT %attributeGroup; ((%annotation;)?,\n                       (%attribute; | %attributeGroup;)*,\n                       (%anyAttribute;)?) >\n<!ATTLIST %attributeGroup;\n                 name       %NCName;       #IMPLIED\n                 id         ID             #IMPLIED\n                 ref        %QName;        #IMPLIED\n                 %attributeGroupAttrs;>\n\n<!-- ref iff no content, no name.  ref iff not top level -->\n\n<!-- better reference mechanisms -->\n<!ELEMENT %unique; ((%annotation;)?, %selector;, (%field;)+)>\n<!ATTLIST %unique;\n          name     %NCName;       #REQUIRED\n\t  id       ID             #IMPLIED\n\t  %uniqueAttrs;>\n\n<!ELEMENT %key;    ((%annotation;)?, %selector;, (%field;)+)>\n<!ATTLIST %key;\n          name     %NCName;       #REQUIRED\n\t  id       ID             #IMPLIED\n\t  %keyAttrs;>\n\n<!ELEMENT %keyref; ((%annotation;)?, %selector;, (%field;)+)>\n<!ATTLIST %keyref;\n          name     %NCName;       #REQUIRED\n\t  refer    %QName;        #REQUIRED\n\t  id       ID             #IMPLIED\n\t  %keyrefAttrs;>\n\n<!ELEMENT %selector; ((%annotation;)?)>\n<!ATTLIST %selector;\n          xpath %XPathExpr; #REQUIRED\n          id    ID          #IMPLIED\n          %selectorAttrs;>\n<!ELEMENT %field; ((%annotation;)?)>\n<!ATTLIST %field;\n          xpath %XPathExpr; #REQUIRED\n          id    ID          #IMPLIED\n          %fieldAttrs;>\n\n<!-- Schema combination mechanisms -->\n<!ELEMENT %include; (%annotation;)?>\n<!ATTLIST %include;\n          schemaLocation %URIref; #REQUIRED\n          id             ID       #IMPLIED\n          %includeAttrs;>\n\n<!ELEMENT %import; (%annotation;)?>\n<!ATTLIST %import;\n          namespace      %URIref; #IMPLIED\n          schemaLocation %URIref; #IMPLIED\n          id             ID       #IMPLIED\n          %importAttrs;>\n\n<!ELEMENT %redefine; (%annotation; | %simpleType; | %complexType; |\n                      %attributeGroup; | %group;)*>\n<!ATTLIST %redefine;\n          schemaLocation %URIref; #REQUIRED\n          id             ID       #IMPLIED\n          %redefineAttrs;>\n\n<!ELEMENT %notation; (%annotation;)?>\n<!ATTLIST %notation;\n\t  name        %NCName;    #REQUIRED\n\t  id          ID          #IMPLIED\n\t  public      CDATA       #REQUIRED\n\t  system      %URIref;    #IMPLIED\n\t  %notationAttrs;>\n\n<!-- Annotation is either application information or documentation -->\n<!-- By having these here they are available for datatypes as well\n     as all the structures elements -->\n\n<!ELEMENT %annotation; (%appinfo; | %documentation;)*>\n<!ATTLIST %annotation; %annotationAttrs;>\n\n<!-- User must define annotation elements in internal subset for this\n     to work -->\n<!ELEMENT %appinfo; ANY>   <!-- too restrictive -->\n<!ATTLIST %appinfo;\n          source     %URIref;      #IMPLIED\n          id         ID         #IMPLIED\n          %appinfoAttrs;>\n<!ELEMENT %documentation; ANY>   <!-- too restrictive -->\n<!ATTLIST %documentation;\n          source     %URIref;   #IMPLIED\n          id         ID         #IMPLIED\n          xml:lang   CDATA      #IMPLIED\n          %documentationAttrs;>\n\n<!NOTATION XMLSchemaStructures PUBLIC\n           'structures' 'http://www.w3.org/2001/XMLSchema.xsd' >\n<!NOTATION XML PUBLIC\n           'REC-xml-1998-0210' 'http://www.w3.org/TR/1998/REC-xml-19980210' >";
export default _default;
