/**
 * أداة لتحويل أوامر سلاش كومند إلى أوامر برفكس
 */
const fs = require('fs');
const path = require('path');

/**
 * تحويل أمر سلاش كومند إلى أمر برفكس
 * @param {string} filePath مسار ملف الأمر
 */
function convertSlashCommandToPrefix(filePath) {
  try {
    // قراءة محتوى ملف الأمر
    let content = fs.readFileSync(filePath, 'utf8');
    
    // الحصول على اسم الأمر من اسم الملف
    const fileName = path.basename(filePath, '.js');
    
    // التحقق إذا كان الملف يحتوي على SlashCommandBuilder
    if (content.includes('SlashCommandBuilder')) {
      console.log(`🔄 جاري تحويل الأمر: ${fileName}`);
      
      // استخراج اسم الأمر ووصفه من الملف
      const nameMatch = content.match(/\.setName\(['"](.+?)['"]\)/);
      const descriptionMatch = content.match(/\.setDescription\(['"](.+?)['"]\)/);
      
      const commandName = nameMatch ? nameMatch[1] : fileName;
      const commandDescription = descriptionMatch ? descriptionMatch[1] : '';
      
      // إنشاء قالب الأمر الجديد
      const newCommandTemplate = `const { EmbedBuilder } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

module.exports = {
  name: '${commandName}',
  description: '${commandDescription}',
  aliases: [], // يمكنك إضافة اختصارات للأمر هنا
  cooldown: 3, // وقت الانتظار بالثواني
  
  async execute(message, args, client) {
    // هنا سيتم تنفيذ الأمر
    // message: كائن الرسالة
    // args: المعاملات المرسلة مع الأمر
    // client: كائن العميل (البوت)
    
    // يمكنك استخدام message.reply() للرد على الرسالة
    // مثال: message.reply('تم تنفيذ الأمر بنجاح!');
  }
};`;
      
      // إنشاء مسار الملف الجديد
      const newFilePath = filePath.replace('.js', '_prefix.js');
      
      // كتابة الملف الجديد
      fs.writeFileSync(newFilePath, newCommandTemplate, 'utf8');
      
      console.log(`✅ تم إنشاء ملف الأمر بنظام البرفكس: ${newFilePath}`);
    } else {
      console.log(`⚠️ الملف ${filePath} لا يحتوي على SlashCommandBuilder. تخطي التحويل.`);
    }
  } catch (error) {
    console.error(`❌ خطأ في تحويل الأمر ${filePath}:`, error);
  }
}

/**
 * تحويل جميع الأوامر في مجلد معين
 * @param {string} directoryPath مسار المجلد
 */
function convertAllCommandsInDirectory(directoryPath) {
  try {
    // قراءة محتويات المجلد
    const items = fs.readdirSync(directoryPath);
    
    for (const item of items) {
      const itemPath = path.join(directoryPath, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        // إذا كان مجلدًا، قم بالبحث داخله بشكل متكرر
        convertAllCommandsInDirectory(itemPath);
      } else if (stats.isFile() && item.endsWith('.js') && !item.endsWith('_prefix.js')) {
        // إذا كان ملف جافاسكريبت وليس ملف برفكس مُحول بالفعل
        convertSlashCommandToPrefix(itemPath);
      }
    }
  } catch (error) {
    console.error(`❌ خطأ في تحويل الأوامر في المجلد ${directoryPath}:`, error);
  }
}

module.exports = {
  convertSlashCommandToPrefix,
  convertAllCommandsInDirectory
};
