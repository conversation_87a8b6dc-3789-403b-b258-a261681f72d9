declare const _default: "<schema\n    targetNamespace=\"urn:oasis:names:tc:SAML:2.0:assertion\"\n    xmlns=\"http://www.w3.org/2001/XMLSchema\"\n    xmlns:saml=\"urn:oasis:names:tc:SAML:2.0:assertion\"\n    xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\"\n    xmlns:xenc=\"http://www.w3.org/2001/04/xmlenc#\"\n    elementFormDefault=\"unqualified\"\n    attributeFormDefault=\"unqualified\"\n    blockDefault=\"substitution\"\n    version=\"2.0\">\n    <import namespace=\"http://www.w3.org/2000/09/xmldsig#\"\n        schemaLocation=\"xmldsig-core-schema.xsd\"/>\n    <import namespace=\"http://www.w3.org/2001/04/xmlenc#\"\n        schemaLocation=\"xenc-schema.xsd\"/>\n    <annotation>\n        <documentation>\n            Document identifier: saml-schema-assertion-2.0\n            Location: http://docs.oasis-open.org/security/saml/v2.0/\n            Revision history:\n            V1.0 (November, 2002):\n              Initial Standard Schema.\n            V1.1 (September, 2003):\n              Updates within the same V1.0 namespace.\n            V2.0 (March, 2005):\n              New assertion schema for SAML V2.0 namespace.\n        </documentation>\n    </annotation>\n    <attributeGroup name=\"IDNameQualifiers\">\n        <attribute name=\"NameQualifier\" type=\"string\" use=\"optional\"/>\n        <attribute name=\"SPNameQualifier\" type=\"string\" use=\"optional\"/>\n    </attributeGroup>\n    <element name=\"BaseID\" type=\"saml:BaseIDAbstractType\"/>\n    <complexType name=\"BaseIDAbstractType\" abstract=\"true\">\n        <attributeGroup ref=\"saml:IDNameQualifiers\"/>\n    </complexType>\n    <element name=\"NameID\" type=\"saml:NameIDType\"/>\n    <complexType name=\"NameIDType\">\n        <simpleContent>\n            <extension base=\"string\">\n                <attributeGroup ref=\"saml:IDNameQualifiers\"/>\n                <attribute name=\"Format\" type=\"anyURI\" use=\"optional\"/>\n                <attribute name=\"SPProvidedID\" type=\"string\" use=\"optional\"/>\n            </extension>\n        </simpleContent>\n    </complexType>\n    <complexType name=\"EncryptedElementType\">\n        <sequence>\n            <element ref=\"xenc:EncryptedData\"/>\n            <element ref=\"xenc:EncryptedKey\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n        </sequence>\n    </complexType>\n    <element name=\"EncryptedID\" type=\"saml:EncryptedElementType\"/>\n    <element name=\"Issuer\" type=\"saml:NameIDType\"/>\n    <element name=\"AssertionIDRef\" type=\"NCName\"/>\n    <element name=\"AssertionURIRef\" type=\"anyURI\"/>\n    <element name=\"Assertion\" type=\"saml:AssertionType\"/>\n    <complexType name=\"AssertionType\">\n        <sequence>\n            <element ref=\"saml:Issuer\"/>\n            <element ref=\"ds:Signature\" minOccurs=\"0\"/>\n            <element ref=\"saml:Subject\" minOccurs=\"0\"/>\n            <element ref=\"saml:Conditions\" minOccurs=\"0\"/>\n            <element ref=\"saml:Advice\" minOccurs=\"0\"/>\n            <choice minOccurs=\"0\" maxOccurs=\"unbounded\">\n                <element ref=\"saml:Statement\"/>\n                <element ref=\"saml:AuthnStatement\"/>\n                <element ref=\"saml:AuthzDecisionStatement\"/>\n                <element ref=\"saml:AttributeStatement\"/>\n            </choice>\n        </sequence>\n        <attribute name=\"Version\" type=\"string\" use=\"required\"/>\n        <attribute name=\"ID\" type=\"ID\" use=\"required\"/>\n        <attribute name=\"IssueInstant\" type=\"dateTime\" use=\"required\"/>\n    </complexType>\n    <element name=\"Subject\" type=\"saml:SubjectType\"/>\n    <complexType name=\"SubjectType\">\n        <choice>\n            <sequence>\n                <choice>\n                    <element ref=\"saml:BaseID\"/>\n                    <element ref=\"saml:NameID\"/>\n                    <element ref=\"saml:EncryptedID\"/>\n                </choice>\n                <element ref=\"saml:SubjectConfirmation\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n            </sequence>\n            <element ref=\"saml:SubjectConfirmation\" maxOccurs=\"unbounded\"/>\n        </choice>\n    </complexType>\n    <element name=\"SubjectConfirmation\" type=\"saml:SubjectConfirmationType\"/>\n    <complexType name=\"SubjectConfirmationType\">\n        <sequence>\n            <choice minOccurs=\"0\">\n                <element ref=\"saml:BaseID\"/>\n                <element ref=\"saml:NameID\"/>\n                <element ref=\"saml:EncryptedID\"/>\n            </choice>\n            <element ref=\"saml:SubjectConfirmationData\" minOccurs=\"0\"/>\n        </sequence>\n        <attribute name=\"Method\" type=\"anyURI\" use=\"required\"/>\n    </complexType>\n    <element name=\"SubjectConfirmationData\" type=\"saml:SubjectConfirmationDataType\"/>\n    <complexType name=\"SubjectConfirmationDataType\" mixed=\"true\">\n        <complexContent>\n            <restriction base=\"anyType\">\n                <sequence>\n                    <any namespace=\"##any\" processContents=\"lax\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n                </sequence>\n                <attribute name=\"NotBefore\" type=\"dateTime\" use=\"optional\"/>\n                <attribute name=\"NotOnOrAfter\" type=\"dateTime\" use=\"optional\"/>\n                <attribute name=\"Recipient\" type=\"anyURI\" use=\"optional\"/>\n                <attribute name=\"InResponseTo\" type=\"NCName\" use=\"optional\"/>\n                <attribute name=\"Address\" type=\"string\" use=\"optional\"/>\n                <anyAttribute namespace=\"##other\" processContents=\"lax\"/>\n            </restriction>\n        </complexContent>\n    </complexType>\n    <complexType name=\"KeyInfoConfirmationDataType\" mixed=\"false\">\n        <complexContent>\n            <restriction base=\"saml:SubjectConfirmationDataType\">\n                <sequence>\n                    <element ref=\"ds:KeyInfo\" maxOccurs=\"unbounded\"/>\n                </sequence>\n            </restriction>\n        </complexContent>\n    </complexType>\n    <element name=\"Conditions\" type=\"saml:ConditionsType\"/>\n    <complexType name=\"ConditionsType\">\n        <choice minOccurs=\"0\" maxOccurs=\"unbounded\">\n            <element ref=\"saml:Condition\"/>\n            <element ref=\"saml:AudienceRestriction\"/>\n            <element ref=\"saml:OneTimeUse\"/>\n            <element ref=\"saml:ProxyRestriction\"/>\n        </choice>\n        <attribute name=\"NotBefore\" type=\"dateTime\" use=\"optional\"/>\n        <attribute name=\"NotOnOrAfter\" type=\"dateTime\" use=\"optional\"/>\n    </complexType>\n    <element name=\"Condition\" type=\"saml:ConditionAbstractType\"/>\n    <complexType name=\"ConditionAbstractType\" abstract=\"true\"/>\n    <element name=\"AudienceRestriction\" type=\"saml:AudienceRestrictionType\"/>\n    <complexType name=\"AudienceRestrictionType\">\n        <complexContent>\n            <extension base=\"saml:ConditionAbstractType\">\n                <sequence>\n                    <element ref=\"saml:Audience\" maxOccurs=\"unbounded\"/>\n                </sequence>\n            </extension>\n        </complexContent>\n    </complexType>\n    <element name=\"Audience\" type=\"anyURI\"/>\n    <element name=\"OneTimeUse\" type=\"saml:OneTimeUseType\" />\n    <complexType name=\"OneTimeUseType\">\n        <complexContent>\n            <extension base=\"saml:ConditionAbstractType\"/>\n        </complexContent>\n    </complexType>\n    <element name=\"ProxyRestriction\" type=\"saml:ProxyRestrictionType\"/>\n    <complexType name=\"ProxyRestrictionType\">\n    <complexContent>\n        <extension base=\"saml:ConditionAbstractType\">\n            <sequence>\n                <element ref=\"saml:Audience\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n            </sequence>\n            <attribute name=\"Count\" type=\"nonNegativeInteger\" use=\"optional\"/>\n        </extension>\n\t</complexContent>\n    </complexType>\n    <element name=\"Advice\" type=\"saml:AdviceType\"/>\n    <complexType name=\"AdviceType\">\n        <choice minOccurs=\"0\" maxOccurs=\"unbounded\">\n            <element ref=\"saml:AssertionIDRef\"/>\n            <element ref=\"saml:AssertionURIRef\"/>\n            <element ref=\"saml:Assertion\"/>\n            <element ref=\"saml:EncryptedAssertion\"/>\n            <any namespace=\"##other\" processContents=\"lax\"/>\n        </choice>\n    </complexType>\n    <element name=\"EncryptedAssertion\" type=\"saml:EncryptedElementType\"/>\n    <element name=\"Statement\" type=\"saml:StatementAbstractType\"/>\n    <complexType name=\"StatementAbstractType\" abstract=\"true\"/>\n    <element name=\"AuthnStatement\" type=\"saml:AuthnStatementType\"/>\n    <complexType name=\"AuthnStatementType\">\n        <complexContent>\n            <extension base=\"saml:StatementAbstractType\">\n                <sequence>\n                    <element ref=\"saml:SubjectLocality\" minOccurs=\"0\"/>\n                    <element ref=\"saml:AuthnContext\"/>\n                </sequence>\n                <attribute name=\"AuthnInstant\" type=\"dateTime\" use=\"required\"/>\n                <attribute name=\"SessionIndex\" type=\"string\" use=\"optional\"/>\n                <attribute name=\"SessionNotOnOrAfter\" type=\"dateTime\" use=\"optional\"/>\n            </extension>\n        </complexContent>\n    </complexType>\n    <element name=\"SubjectLocality\" type=\"saml:SubjectLocalityType\"/>\n    <complexType name=\"SubjectLocalityType\">\n        <attribute name=\"Address\" type=\"string\" use=\"optional\"/>\n        <attribute name=\"DNSName\" type=\"string\" use=\"optional\"/>\n    </complexType>\n    <element name=\"AuthnContext\" type=\"saml:AuthnContextType\"/>\n    <complexType name=\"AuthnContextType\">\n        <sequence>\n            <choice>\n                <sequence>\n                    <element ref=\"saml:AuthnContextClassRef\"/>\n                    <choice minOccurs=\"0\">\n                        <element ref=\"saml:AuthnContextDecl\"/>\n                        <element ref=\"saml:AuthnContextDeclRef\"/>\n                    </choice>\n                </sequence>\n                <choice>\n                    <element ref=\"saml:AuthnContextDecl\"/>\n                    <element ref=\"saml:AuthnContextDeclRef\"/>\n                </choice>\n            </choice>\n            <element ref=\"saml:AuthenticatingAuthority\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n        </sequence>\n    </complexType>\n    <element name=\"AuthnContextClassRef\" type=\"anyURI\"/>\n    <element name=\"AuthnContextDeclRef\" type=\"anyURI\"/>\n    <element name=\"AuthnContextDecl\" type=\"anyType\"/>\n    <element name=\"AuthenticatingAuthority\" type=\"anyURI\"/>\n    <element name=\"AuthzDecisionStatement\" type=\"saml:AuthzDecisionStatementType\"/>\n    <complexType name=\"AuthzDecisionStatementType\">\n        <complexContent>\n            <extension base=\"saml:StatementAbstractType\">\n                <sequence>\n                    <element ref=\"saml:Action\" maxOccurs=\"unbounded\"/>\n                    <element ref=\"saml:Evidence\" minOccurs=\"0\"/>\n                </sequence>\n                <attribute name=\"Resource\" type=\"anyURI\" use=\"required\"/>\n                <attribute name=\"Decision\" type=\"saml:DecisionType\" use=\"required\"/>\n            </extension>\n        </complexContent>\n    </complexType>\n    <simpleType name=\"DecisionType\">\n        <restriction base=\"string\">\n            <enumeration value=\"Permit\"/>\n            <enumeration value=\"Deny\"/>\n            <enumeration value=\"Indeterminate\"/>\n        </restriction>\n    </simpleType>\n    <element name=\"Action\" type=\"saml:ActionType\"/>\n    <complexType name=\"ActionType\">\n        <simpleContent>\n            <extension base=\"string\">\n                <attribute name=\"Namespace\" type=\"anyURI\" use=\"required\"/>\n            </extension>\n        </simpleContent>\n    </complexType>\n    <element name=\"Evidence\" type=\"saml:EvidenceType\"/>\n    <complexType name=\"EvidenceType\">\n        <choice maxOccurs=\"unbounded\">\n            <element ref=\"saml:AssertionIDRef\"/>\n            <element ref=\"saml:AssertionURIRef\"/>\n            <element ref=\"saml:Assertion\"/>\n            <element ref=\"saml:EncryptedAssertion\"/>\n        </choice>\n    </complexType>\n    <element name=\"AttributeStatement\" type=\"saml:AttributeStatementType\"/>\n    <complexType name=\"AttributeStatementType\">\n        <complexContent>\n            <extension base=\"saml:StatementAbstractType\">\n                <choice maxOccurs=\"unbounded\">\n                    <element ref=\"saml:Attribute\"/>\n                    <element ref=\"saml:EncryptedAttribute\"/>\n                </choice>\n            </extension>\n        </complexContent>\n    </complexType>\n    <element name=\"Attribute\" type=\"saml:AttributeType\"/>\n    <complexType name=\"AttributeType\">\n        <sequence>\n            <element ref=\"saml:AttributeValue\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n        </sequence>\n        <attribute name=\"Name\" type=\"string\" use=\"required\"/>\n        <attribute name=\"NameFormat\" type=\"anyURI\" use=\"optional\"/>\n        <attribute name=\"FriendlyName\" type=\"string\" use=\"optional\"/>\n        <anyAttribute namespace=\"##other\" processContents=\"lax\"/>\n    </complexType>\n    <element name=\"AttributeValue\" type=\"anyType\" nillable=\"true\"/>\n    <element name=\"EncryptedAttribute\" type=\"saml:EncryptedElementType\"/>\n</schema>";
export default _default;
