const mongoose = require('mongoose');

const TransactionSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['deposit', 'withdraw', 'transfer', 'daily', 'admin_balance_update', 'roulette', 'slots', 'blackjack', 'dice', 'wheel', 'other']
  },
  userId: {
    type: String,
    required: true
  },
  username: {
    type: String,
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  // للتحويلات
  receiverId: {
    type: String,
    default: null
  },
  receiverName: {
    type: String,
    default: null
  },
  // للعمليات الإدارية
  adminId: {
    type: String,
    default: null
  },
  adminName: {
    type: String,
    default: null
  },
  // للألعاب
  gameDetails: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  },
  result: {
    type: String,
    enum: ['win', 'lose', 'push', 'blackjack', 'bust', 'surrender', 'timeout', null],
    default: null
  },
  profit: {
    type: Number,
    default: 0
  },
  // معلومات عامة
  timestamp: {
    type: Date,
    default: Date.now
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
});

// إضافة فهرس للبحث السريع عن المعاملات
TransactionSchema.index({ userId: 1, timestamp: -1 });
TransactionSchema.index({ type: 1, timestamp: -1 });

module.exports = mongoose.model('Transaction', TransactionSchema);
