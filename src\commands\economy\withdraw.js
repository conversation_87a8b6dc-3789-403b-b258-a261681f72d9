const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('سحب')
    .setDescription('سحب الأموال من البنك')
    .addStringOption(option => 
      option.setName('المبلغ')
        .setDescription('المبلغ المراد سحبه (يمكنك كتابة "الكل" لسحب كل رصيدك)')
        .setRequired(true)),
  
  async execute(interaction) {
    await interaction.deferReply();
    
    const amountInput = interaction.options.getString('المبلغ');
    let amount;
    
    try {
      // البحث عن حساب المستخدم
      let userProfile = await User.findOne({ userId: interaction.user.id });
      
      if (!userProfile) {
        // إنشاء حساب جديد للمستخدم إذا لم يكن موجودًا
        userProfile = await User.create({
          userId: interaction.user.id,
          username: interaction.user.username,
          balance: 1000,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // التحقق من المبلغ المدخل
      if (amountInput.toLowerCase() === 'الكل' || amountInput.toLowerCase() === 'all') {
        amount = userProfile.bank;
      } else {
        amount = parseInt(amountInput);
        
        if (isNaN(amount) || amount <= 0) {
          return interaction.editReply('❌ يرجى إدخال مبلغ صحيح أكبر من صفر أو كتابة "الكل".');
        }
      }
      
      // التحقق من وجود رصيد كافٍ في البنك
      if (userProfile.bank < amount) {
        return interaction.editReply(`❌ ليس لديك رصيد كافٍ في البنك! رصيدك الحالي في البنك هو ${formatNumber(userProfile.bank)} 💲`);
      }
      
      // إجراء عملية السحب
      userProfile.bank -= amount;
      userProfile.balance += amount;
      
      // حفظ التغييرات
      await userProfile.save();
      
      // تسجيل المعاملة
      logTransaction({
        type: 'withdraw',
        userId: interaction.user.id,
        username: interaction.user.username,
        amount: amount,
        timestamp: new Date()
      });
      
      // إنشاء رسالة تأكيد
      const embed = new EmbedBuilder()
        .setTitle('💰 سحب ناجح')
        .setColor('#4CAF50')
        .setDescription(`تم سحب ${formatNumber(amount)} 💲 من حسابك المصرفي بنجاح!`)
        .addFields(
          { name: '💵 النقود في اليد', value: `${formatNumber(userProfile.balance)} 💲`, inline: true },
          { name: '🏦 النقود في البنك', value: `${formatNumber(userProfile.bank)} 💲`, inline: true }
        )
        .setFooter({ text: `معرف المعاملة: ${Date.now().toString(36).toUpperCase()}` })
        .setTimestamp();
      
      await interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error('خطأ في أمر السحب:', error);
      await interaction.editReply('حدث خطأ أثناء تنفيذ عملية السحب. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
