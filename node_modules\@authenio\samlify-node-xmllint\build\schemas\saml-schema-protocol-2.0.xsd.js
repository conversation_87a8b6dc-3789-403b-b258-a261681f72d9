"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = "<schema\n    targetNamespace=\"urn:oasis:names:tc:SAML:2.0:protocol\"\n    xmlns=\"http://www.w3.org/2001/XMLSchema\"\n    xmlns:samlp=\"urn:oasis:names:tc:SAML:2.0:protocol\"\n    xmlns:saml=\"urn:oasis:names:tc:SAML:2.0:assertion\"\n    xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\"\n    elementFormDefault=\"unqualified\"\n    attributeFormDefault=\"unqualified\"\n    blockDefault=\"substitution\"\n    version=\"2.0\">\n    <import namespace=\"urn:oasis:names:tc:SAML:2.0:assertion\"\n        schemaLocation=\"saml-schema-assertion-2.0.xsd\"/>\n    <import namespace=\"http://www.w3.org/2000/09/xmldsig#\"\n        schemaLocation=\"xmldsig-core-schema.xsd\"/>\n    <annotation>\n        <documentation>\n            Document identifier: saml-schema-protocol-2.0\n            Location: http://docs.oasis-open.org/security/saml/v2.0/\n            Revision history:\n            V1.0 (November, 2002):\n              Initial Standard Schema.\n            V1.1 (September, 2003):\n              Updates within the same V1.0 namespace.\n            V2.0 (March, 2005):\n              New protocol schema based in a SAML V2.0 namespace.\n     </documentation>\n    </annotation>\n    <complexType name=\"RequestAbstractType\" abstract=\"true\">\n        <sequence>\n            <element ref=\"saml:Issuer\" minOccurs=\"0\"/>\n            <element ref=\"ds:Signature\" minOccurs=\"0\"/>\n            <element ref=\"samlp:Extensions\" minOccurs=\"0\"/>\n        </sequence>\n        <attribute name=\"ID\" type=\"ID\" use=\"required\"/>\n        <attribute name=\"Version\" type=\"string\" use=\"required\"/>\n        <attribute name=\"IssueInstant\" type=\"dateTime\" use=\"required\"/>\n        <attribute name=\"Destination\" type=\"anyURI\" use=\"optional\"/>\n    \t<attribute name=\"Consent\" type=\"anyURI\" use=\"optional\"/>\n    </complexType>\n    <element name=\"Extensions\" type=\"samlp:ExtensionsType\"/>\n    <complexType name=\"ExtensionsType\">\n        <sequence>\n            <any namespace=\"##other\" processContents=\"lax\" maxOccurs=\"unbounded\"/>\n        </sequence>\n    </complexType>\n    <complexType name=\"StatusResponseType\">\n    \t<sequence>\n            <element ref=\"saml:Issuer\" minOccurs=\"0\"/>\n            <element ref=\"ds:Signature\" minOccurs=\"0\"/>\n            <element ref=\"samlp:Extensions\" minOccurs=\"0\"/>\n            <element ref=\"samlp:Status\"/>\n    \t</sequence>\n    \t<attribute name=\"ID\" type=\"ID\" use=\"required\"/>\n    \t<attribute name=\"InResponseTo\" type=\"NCName\" use=\"optional\"/>\n    \t<attribute name=\"Version\" type=\"string\" use=\"required\"/>\n    \t<attribute name=\"IssueInstant\" type=\"dateTime\" use=\"required\"/>\n    \t<attribute name=\"Destination\" type=\"anyURI\" use=\"optional\"/>\n    \t<attribute name=\"Consent\" type=\"anyURI\" use=\"optional\"/>\n    </complexType>\n    <element name=\"Status\" type=\"samlp:StatusType\"/>\n    <complexType name=\"StatusType\">\n        <sequence>\n            <element ref=\"samlp:StatusCode\"/>\n            <element ref=\"samlp:StatusMessage\" minOccurs=\"0\"/>\n            <element ref=\"samlp:StatusDetail\" minOccurs=\"0\"/>\n        </sequence>\n    </complexType>\n    <element name=\"StatusCode\" type=\"samlp:StatusCodeType\"/>\n    <complexType name=\"StatusCodeType\">\n        <sequence>\n            <element ref=\"samlp:StatusCode\" minOccurs=\"0\"/>\n        </sequence>\n        <attribute name=\"Value\" type=\"anyURI\" use=\"required\"/>\n    </complexType>\n    <element name=\"StatusMessage\" type=\"string\"/>\n    <element name=\"StatusDetail\" type=\"samlp:StatusDetailType\"/>\n    <complexType name=\"StatusDetailType\">\n        <sequence>\n            <any namespace=\"##any\" processContents=\"lax\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n        </sequence>\n    </complexType>\n    <element name=\"AssertionIDRequest\" type=\"samlp:AssertionIDRequestType\"/>\n    <complexType name=\"AssertionIDRequestType\">\n    \t<complexContent>\n            <extension base=\"samlp:RequestAbstractType\">\n                <sequence>\n                    <element ref=\"saml:AssertionIDRef\" maxOccurs=\"unbounded\"/>\n                </sequence>\n            </extension>\n    \t</complexContent>\n    </complexType>\n    <element name=\"SubjectQuery\" type=\"samlp:SubjectQueryAbstractType\"/>\n    <complexType name=\"SubjectQueryAbstractType\" abstract=\"true\">\n    \t<complexContent>\n            <extension base=\"samlp:RequestAbstractType\">\n                <sequence>\n                    <element ref=\"saml:Subject\"/>\n                </sequence>\n            </extension>\n    \t</complexContent>\n    </complexType>\n    <element name=\"AuthnQuery\" type=\"samlp:AuthnQueryType\"/>\n    <complexType name=\"AuthnQueryType\">\n        <complexContent>\n            <extension base=\"samlp:SubjectQueryAbstractType\">\n                <sequence>\n                    <element ref=\"samlp:RequestedAuthnContext\" minOccurs=\"0\"/>\n                </sequence>\n                <attribute name=\"SessionIndex\" type=\"string\" use=\"optional\"/>\n            </extension>\n        </complexContent>\n    </complexType>\n    <element name=\"RequestedAuthnContext\" type=\"samlp:RequestedAuthnContextType\"/>\n    <complexType name=\"RequestedAuthnContextType\">\n        <choice>\n            <element ref=\"saml:AuthnContextClassRef\" maxOccurs=\"unbounded\"/>\n            <element ref=\"saml:AuthnContextDeclRef\" maxOccurs=\"unbounded\"/>\n        </choice>\n        <attribute name=\"Comparison\" type=\"samlp:AuthnContextComparisonType\" use=\"optional\"/>\n    </complexType>\n    <simpleType name=\"AuthnContextComparisonType\">\n        <restriction base=\"string\">\n            <enumeration value=\"exact\"/>\n            <enumeration value=\"minimum\"/>\n            <enumeration value=\"maximum\"/>\n            <enumeration value=\"better\"/>\n        </restriction>\n    </simpleType>\n    <element name=\"AttributeQuery\" type=\"samlp:AttributeQueryType\"/>\n    <complexType name=\"AttributeQueryType\">\n        <complexContent>\n            <extension base=\"samlp:SubjectQueryAbstractType\">\n                <sequence>\n                    <element ref=\"saml:Attribute\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n                </sequence>\n            </extension>\n        </complexContent>\n    </complexType>\n    <element name=\"AuthzDecisionQuery\" type=\"samlp:AuthzDecisionQueryType\"/>\n    <complexType name=\"AuthzDecisionQueryType\">\n        <complexContent>\n            <extension base=\"samlp:SubjectQueryAbstractType\">\n                <sequence>\n                    <element ref=\"saml:Action\" maxOccurs=\"unbounded\"/>\n                    <element ref=\"saml:Evidence\" minOccurs=\"0\"/>\n                </sequence>\n                <attribute name=\"Resource\" type=\"anyURI\" use=\"required\"/>\n            </extension>\n        </complexContent>\n    </complexType>\n    <element name=\"AuthnRequest\" type=\"samlp:AuthnRequestType\"/>\n    <complexType name=\"AuthnRequestType\">\n        <complexContent>\n            <extension base=\"samlp:RequestAbstractType\">\n                <sequence>\n                    <element ref=\"saml:Subject\" minOccurs=\"0\"/>\n                    <element ref=\"samlp:NameIDPolicy\" minOccurs=\"0\"/>\n                    <element ref=\"saml:Conditions\" minOccurs=\"0\"/>\n                    <element ref=\"samlp:RequestedAuthnContext\" minOccurs=\"0\"/>\n                    <element ref=\"samlp:Scoping\" minOccurs=\"0\"/>\n                </sequence>\n                <attribute name=\"ForceAuthn\" type=\"boolean\" use=\"optional\"/>\n                <attribute name=\"IsPassive\" type=\"boolean\" use=\"optional\"/>\n                <attribute name=\"ProtocolBinding\" type=\"anyURI\" use=\"optional\"/>\n                <attribute name=\"AssertionConsumerServiceIndex\" type=\"unsignedShort\" use=\"optional\"/>\n                <attribute name=\"AssertionConsumerServiceURL\" type=\"anyURI\" use=\"optional\"/>\n                <attribute name=\"AttributeConsumingServiceIndex\" type=\"unsignedShort\" use=\"optional\"/>\n                <attribute name=\"ProviderName\" type=\"string\" use=\"optional\"/>\n            </extension>\n        </complexContent>\n    </complexType>\n    <element name=\"NameIDPolicy\" type=\"samlp:NameIDPolicyType\"/>\n    <complexType name=\"NameIDPolicyType\">\n        <attribute name=\"Format\" type=\"anyURI\" use=\"optional\"/>\n        <attribute name=\"SPNameQualifier\" type=\"string\" use=\"optional\"/>\n        <attribute name=\"AllowCreate\" type=\"boolean\" use=\"optional\"/>\n    </complexType>\n    <element name=\"Scoping\" type=\"samlp:ScopingType\"/>\n    <complexType name=\"ScopingType\">\n        <sequence>\n            <element ref=\"samlp:IDPList\" minOccurs=\"0\"/>\n            <element ref=\"samlp:RequesterID\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n        </sequence>\n        <attribute name=\"ProxyCount\" type=\"nonNegativeInteger\" use=\"optional\"/>\n    </complexType>\n    <element name=\"RequesterID\" type=\"anyURI\"/>\n    <element name=\"IDPList\" type=\"samlp:IDPListType\"/>\n    <complexType name=\"IDPListType\">\n        <sequence>\n            <element ref=\"samlp:IDPEntry\" maxOccurs=\"unbounded\"/>\n            <element ref=\"samlp:GetComplete\" minOccurs=\"0\"/>\n        </sequence>\n    </complexType>\n    <element name=\"IDPEntry\" type=\"samlp:IDPEntryType\"/>\n    <complexType name=\"IDPEntryType\">\n        <attribute name=\"ProviderID\" type=\"anyURI\" use=\"required\"/>\n        <attribute name=\"Name\" type=\"string\" use=\"optional\"/>\n        <attribute name=\"Loc\" type=\"anyURI\" use=\"optional\"/>\n    </complexType>\n    <element name=\"GetComplete\" type=\"anyURI\"/>\n    <element name=\"Response\" type=\"samlp:ResponseType\"/>\n    <complexType name=\"ResponseType\">\n    \t<complexContent>\n            <extension base=\"samlp:StatusResponseType\">\n                <choice minOccurs=\"0\" maxOccurs=\"unbounded\">\n                    <element ref=\"saml:Assertion\"/>\n                    <element ref=\"saml:EncryptedAssertion\"/>\n                </choice>\n            </extension>\n    \t</complexContent>\n    </complexType>\n    <element name=\"ArtifactResolve\" type=\"samlp:ArtifactResolveType\"/>\n    <complexType name=\"ArtifactResolveType\">\n    \t<complexContent>\n            <extension base=\"samlp:RequestAbstractType\">\n                <sequence>\n                    <element ref=\"samlp:Artifact\"/>\n                </sequence>\n            </extension>\n    \t</complexContent>\n    </complexType>\n    <element name=\"Artifact\" type=\"string\"/>\n    <element name=\"ArtifactResponse\" type=\"samlp:ArtifactResponseType\"/>\n    <complexType name=\"ArtifactResponseType\">\n    \t<complexContent>\n            <extension base=\"samlp:StatusResponseType\">\n                <sequence>\n                    <any namespace=\"##any\" processContents=\"lax\" minOccurs=\"0\"/>\n                </sequence>\n            </extension>\n    \t</complexContent>\n    </complexType>\n    <element name=\"ManageNameIDRequest\" type=\"samlp:ManageNameIDRequestType\"/>\n    <complexType name=\"ManageNameIDRequestType\">\n    \t<complexContent>\n            <extension base=\"samlp:RequestAbstractType\">\n                <sequence>\n                    <choice>\n                        <element ref=\"saml:NameID\"/>\n                        <element ref=\"saml:EncryptedID\"/>\n                    </choice>\n                    <choice>\n                        <element ref=\"samlp:NewID\"/>\n                        <element ref=\"samlp:NewEncryptedID\"/>\n                        <element ref=\"samlp:Terminate\"/>\n                    </choice>\n                </sequence>\n            </extension>\n    \t</complexContent>\n    </complexType>\n    <element name=\"NewID\" type=\"string\"/>\n    <element name=\"NewEncryptedID\" type=\"saml:EncryptedElementType\"/>\n    <element name=\"Terminate\" type=\"samlp:TerminateType\"/>\n    <complexType name=\"TerminateType\"/>\n    <element name=\"ManageNameIDResponse\" type=\"samlp:StatusResponseType\"/>\n    <element name=\"LogoutRequest\" type=\"samlp:LogoutRequestType\"/>\n    <complexType name=\"LogoutRequestType\">\n        <complexContent>\n            <extension base=\"samlp:RequestAbstractType\">\n                <sequence>\n                    <choice>\n                        <element ref=\"saml:BaseID\"/>\n                        <element ref=\"saml:NameID\"/>\n                        <element ref=\"saml:EncryptedID\"/>\n                    </choice>\n                    <element ref=\"samlp:SessionIndex\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n                </sequence>\n                <attribute name=\"Reason\" type=\"string\" use=\"optional\"/>\n                <attribute name=\"NotOnOrAfter\" type=\"dateTime\" use=\"optional\"/>\n            </extension>\n        </complexContent>\n    </complexType>\n    <element name=\"SessionIndex\" type=\"string\"/>\n    <element name=\"LogoutResponse\" type=\"samlp:StatusResponseType\"/>\n    <element name=\"NameIDMappingRequest\" type=\"samlp:NameIDMappingRequestType\"/>\n    <complexType name=\"NameIDMappingRequestType\">\n        <complexContent>\n            <extension base=\"samlp:RequestAbstractType\">\n                <sequence>\n                    <choice>\n                        <element ref=\"saml:BaseID\"/>\n                        <element ref=\"saml:NameID\"/>\n                        <element ref=\"saml:EncryptedID\"/>\n                    </choice>\n                    <element ref=\"samlp:NameIDPolicy\"/>\n                </sequence>\n            </extension>\n        </complexContent>\n    </complexType>\n    <element name=\"NameIDMappingResponse\" type=\"samlp:NameIDMappingResponseType\"/>\n    <complexType name=\"NameIDMappingResponseType\">\n        <complexContent>\n            <extension base=\"samlp:StatusResponseType\">\n                <choice>\n                    <element ref=\"saml:NameID\"/>\n                    <element ref=\"saml:EncryptedID\"/>\n                </choice>\n            </extension>\n        </complexContent>\n    </complexType>\n</schema>";
//# sourceMappingURL=saml-schema-protocol-2.0.xsd.js.map