module.exports = {
  name: 'ready',
  once: true,
  execute(client) {
    console.log(`🎮 تم تسجيل الدخول باسم ${client.user.tag}!`);
    
    // تعيين حالة البوت
    client.user.setPresence({
      activities: [
        {
          name: `${client.config.prefix}مساعدة`,
          type: 3 // WATCHING
        }
      ],
      status: 'online'
    });
    
    // طباعة إحصائيات البوت
    console.log(`📊 البوت متصل بـ ${client.guilds.cache.size} سيرفر`);
    console.log(`👥 يخدم ${client.users.cache.size} مستخدم`);
    console.log(`🔄 البرفكس: ${client.config.prefix}`);
    
    // إنشاء تصفح السيرفرات كل 30 دقيقة لتحديث الإحصائيات
    setInterval(() => {
      client.user.setPresence({
        activities: [
          {
            name: `${client.config.prefix}مساعدة | ${client.guilds.cache.size} سيرفر`,
            type: 3 // WATCHING
          }
        ],
        status: 'online'
      });
    }, 30 * 60 * 1000); // 30 دقيقة
  }
};
