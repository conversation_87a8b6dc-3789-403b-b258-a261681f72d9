{"name": "@aws-crypto/supports-web-crypto", "version": "3.0.0", "description": "Provides functions for detecting if the host environment supports the WebCrypto API", "scripts": {"pretest": "tsc -p tsconfig.test.json", "test": "mocha --require ts-node/register test/**/*test.ts"}, "repository": {"type": "git", "url": "**************:aws/aws-sdk-js-crypto-helpers.git"}, "author": {"name": "AWS Crypto Tools Team", "email": "<EMAIL>", "url": "https://docs.aws.amazon.com/aws-crypto-tools/index.html?id=docs_gateway#lang/en_us"}, "homepage": "https://github.com/aws/aws-sdk-js-crypto-helpers/tree/master/packages/supports-web-crypto", "license": "Apache-2.0", "main": "./build/index.js", "types": "./build/index.d.ts", "dependencies": {"tslib": "^1.11.1"}, "gitHead": "7f56cee8f62bd65cd397eeec29c3c997215bd80c"}