{"name": "tslib", "author": "Microsoft Corp.", "homepage": "https://www.typescriptlang.org/", "version": "1.14.1", "license": "0BSD", "description": "Runtime library for TypeScript helper functions", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/tslib.git"}, "main": "tslib.js", "module": "tslib.es6.js", "jsnext:main": "tslib.es6.js", "typings": "tslib.d.ts", "sideEffects": false, "exports": {".": {"module": "./tslib.es6.js", "import": "./modules/index.js", "default": "./tslib.js"}, "./": "./"}}