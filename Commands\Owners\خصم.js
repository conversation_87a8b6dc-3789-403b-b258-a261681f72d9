const Data = require('pro.db');
const { MessageEmbed } = require('discord.js');
const { owners } = require(`${process.cwd()}/config`);

module.exports = {
    name: "خصم",
    description: "خصم مبلغ من رصيد عضو معين",
    run: async (client, message, args) => {
        if (!owners.includes(message.author.id)) return message.react('❌');

        const targetUser = message.mentions.users.first();
        if (!targetUser) return message.reply("**يرجى إرفاق منشن الشخص والمبلغ.**");

        const amount = parseInt(args[1]);
        if (isNaN(amount) || amount <= 0) return message.react("❎");

        const userBalance = await Data.get(`money_${targetUser.id}`);
        if (!userBalance || userBalance < amount) return message.reply("لا يمتلك العضو المبلغ الكافي في رصيده");

        await Data.subtract(`money_${targetUser.id}`, amount);

        message.react("☑️");

        // تسجيل اللوج
        const logChannelId = await Data.get(`logChannel_${message.guild.id}`);
        const logChannel = message.guild.channels.cache.get(logChannelId);

        if (logChannel) {
            const logEmbed = new MessageEmbed()
                .setTitle("خصم رصيد")
                .setDescription(`**تم خصم مبلغ قدره (${amount.toLocaleString('en-US')})$ من رصيد العضو : <@${targetUser.id}> 
                 من قبل : <@${message.author.id}>**`)
                .addField("المبلغ", `${amount.toLocaleString('en-US')}$`)
                .setThumbnail('https://i.top4top.io/p_3127n27gz1.png')
               
                .setColor("#000")
                .setFooter({ text: 'Sky Store', iconURL: 'https://k.top4top.io/p_3299aejba1.png' });
        
            logChannel.send({ embeds: [logEmbed] });
        }        
    }
};
