const { EmbedBuilder } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');

module.exports = {
  name: 'رصيد',
  description: 'عرض رصيدك الحالي في البنك',
  aliases: ['حساب', 'فلوس', 'balance'],
  cooldown: 3,
  
  async execute(message, args, client) {
    // التحقق إذا كان هناك مستخدم تمت الإشارة إليه
    const targetUser = message.mentions.users.first() || message.author;
    
    try {
      let userProfile = await User.findOne({ userId: targetUser.id });
      
      if (!userProfile) {
        // إذا لم يكن المستخدم موجودًا، قم بإنشاء حساب جديد له
        userProfile = await User.create({
          userId: targetUser.id,
          username: targetUser.username,
          balance: 1000, // رصيد ابتدائي
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      const embed = new EmbedBuilder()
        .setTitle(`💰 رصيد ${targetUser.username}`)
        .setDescription(`معلومات الحساب المصرفي`)
        .setColor('#FFD700')
        .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
        .addFields(
          { name: '💵 النقود في اليد', value: `${formatNumber(userProfile.balance)} 💲`, inline: true },
          { name: '🏦 النقود في البنك', value: `${formatNumber(userProfile.bank)} 💲`, inline: true },
          { name: '💸 المجموع', value: `${formatNumber(userProfile.balance + userProfile.bank)} 💲`, inline: true }
        )
        .setFooter({ text: `تم إنشاء الحساب في: ${userProfile.createdAt.toLocaleDateString('ar-SA')}` })
        .setTimestamp();
      
      message.reply({ embeds: [embed] });
    } catch (error) {
      console.error('خطأ في أمر الرصيد:', error);
      message.reply('حدث خطأ أثناء جلب معلومات الرصيد. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
