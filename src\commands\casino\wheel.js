const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

// إعداد عجلة الحظ
const wheelSegments = [
  { value: 0.2, label: '0.2x', color: '#F44336' },
  { value: 0.5, label: '0.5x', color: '#FF9800' },
  { value: 0.8, label: '0.8x', color: '#FFC107' },
  { value: 1, label: '1x', color: '#FFEB3B' },
  { value: 1.2, label: '1.2x', color: '#CDDC39' },
  { value: 1.5, label: '1.5x', color: '#8BC34A' },
  { value: 1.7, label: '1.7x', color: '#4CAF50' },
  { value: 2, label: '2x', color: '#009688' },
  { value: 0, label: '0x', color: '#000000' },
  { value: 3, label: '3x', color: '#2196F3' },
  { value: 5, label: '5x', color: '#3F51B5' },
  { value: 0, label: '0x', color: '#000000' },
];

module.exports = {
  data: new SlashCommandBuilder()
    .setName('عجلة_الحظ')
    .setDescription('لعب عجلة الحظ بمبلغ معين')
    .addIntegerOption(option => 
      option.setName('المبلغ')
        .setDescription('المبلغ المراد المراهنة به')
        .setRequired(true)
        .setMinValue(100)),
  
  async execute(interaction) {
    await interaction.deferReply();
    
    const amount = interaction.options.getInteger('المبلغ');
    
    try {
      // البحث عن حساب المستخدم
      let userProfile = await User.findOne({ userId: interaction.user.id });
      
      if (!userProfile) {
        // إنشاء حساب جديد للمستخدم إذا لم يكن موجودًا
        userProfile = await User.create({
          userId: interaction.user.id,
          username: interaction.user.username,
          balance: 1000,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // التحقق من وجود رصيد كافٍ
      if (userProfile.balance < amount) {
        return interaction.editReply(`❌ ليس لديك رصيد كافٍ! رصيدك الحالي هو ${formatNumber(userProfile.balance)} 💲`);
      }
      
      // خصم المبلغ
      userProfile.balance -= amount;
      await userProfile.save();
      
      // إنشاء زر لتدوير العجلة
      const row = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('spin_wheel')
            .setLabel('تدوير العجلة')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('🎡')
        );
      
      // إنشاء رسالة الانتظار
      const waitingEmbed = new EmbedBuilder()
        .setTitle('🎡 عجلة الحظ')
        .setColor('#9C27B0')
        .setDescription(`لقد راهنت بمبلغ ${formatNumber(amount)} 💲\nاضغط على الزر لتدوير العجلة!`)
        .setFooter({ text: 'حظًا موفقًا!' })
        .setTimestamp();
      
      const response = await interaction.editReply({ embeds: [waitingEmbed], components: [row] });
      
      // إنشاء مستمع للزر
      const filter = i => i.customId === 'spin_wheel' && i.user.id === interaction.user.id;
      const collector = response.createMessageComponentCollector({ filter, time: 30000, max: 1 });
      
      collector.on('collect', async buttonInteraction => {
        // تحديث حالة الزر
        await buttonInteraction.update({
          content: '🎡 جاري تدوير العجلة...',
          components: []
        });
        
        // محاكاة تدوير العجلة
        setTimeout(async () => {
          // اختيار نتيجة عشوائية
          const randomIndex = Math.floor(Math.random() * wheelSegments.length);
          const result = wheelSegments[randomIndex];
          
          // حساب المبلغ المربوح
          const winMultiplier = result.value;
          const winAmount = Math.floor(amount * winMultiplier);
          
          // إضافة المبلغ المربوح
          userProfile.balance += winAmount;
          await userProfile.save();
          
          // تسجيل المعاملة
          logTransaction({
            type: 'wheel',
            userId: interaction.user.id,
            username: interaction.user.username,
            amount: amount,
            multiplier: winMultiplier,
            result: winAmount > 0 ? 'win' : 'lose',
            profit: winAmount - amount,
            timestamp: new Date()
          });
          
          // تحديد لون النتيجة
          const resultColor = result.color;
          
          // إنشاء وصف النتيجة
          let resultDescription;
          if (winAmount > amount) {
            resultDescription = `🎉 **ربحت!** العجلة توقفت على ${result.label}\nلقد ربحت ${formatNumber(winAmount - amount)} 💲`;
          } else if (winAmount === amount) {
            resultDescription = `🤝 **استرداد!** العجلة توقفت على ${result.label}\nتم إرجاع مبلغ الرهان ${formatNumber(amount)} 💲`;
          } else {
            resultDescription = `❌ **خسرت!** العجلة توقفت على ${result.label}\nلقد خسرت ${formatNumber(amount - winAmount)} 💲`;
          }
          
          // إنشاء رسالة النتيجة
          const resultEmbed = new EmbedBuilder()
            .setTitle('🎡 عجلة الحظ')
            .setColor(resultColor)
            .setDescription(resultDescription)
            .addFields(
              { name: 'المبلغ المراهن به', value: `${formatNumber(amount)} 💲`, inline: true },
              { name: 'المضاعف', value: `${result.label}`, inline: true },
              { name: 'المبلغ المربوح', value: `${formatNumber(winAmount)} 💲`, inline: true },
              { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
            )
            .setFooter({ text: winAmount > amount ? 'مبروك! 🎉' : (winAmount === amount ? 'استرداد! 🤝' : 'حظًا أوفر في المرة القادمة! ❌') })
            .setTimestamp();
          
          await buttonInteraction.editReply({ content: null, embeds: [resultEmbed], components: [] });
        }, 3000); // تأخير 3 ثوان لتعزيز التشويق
      });
      
      collector.on('end', async collected => {
        if (collected.size === 0) {
          // لم يتم الضغط على الزر
          // إعادة المبلغ
          userProfile.balance += amount;
          await userProfile.save();
          
          // إنشاء رسالة الإلغاء
          const cancelEmbed = new EmbedBuilder()
            .setTitle('🎡 عجلة الحظ')
            .setColor('#808080')
            .setDescription(`**تم إلغاء اللعبة! ⏰**\nتم إرجاع مبلغ الرهان ${formatNumber(amount)} 💲.`)
            .addFields(
              { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
            )
            .setFooter({ text: 'انتهت مهلة اللعبة' })
            .setTimestamp();
          
          await interaction.editReply({ embeds: [cancelEmbed], components: [] });
        }
      });
    } catch (error) {
      console.error('خطأ في أمر عجلة الحظ:', error);
      await interaction.editReply('حدث خطأ أثناء تنفيذ لعبة عجلة الحظ. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
