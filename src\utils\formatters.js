/**
 * تنسيق الأرقام بإضافة فواصل للآلاف
 * @param {number} number الرقم المراد تنسيقه
 * @returns {string} الرقم بعد التنسيق
 */
function formatNumber(number) {
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

/**
 * تنسيق التاريخ والوقت بتنسيق محلي
 * @param {Date} date التاريخ المراد تنسيقه
 * @returns {string} التاريخ المنسق
 */
function formatDate(date) {
  return new Date(date).toLocaleString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * تنسيق المدة الزمنية
 * @param {number} ms المدة بالمللي ثانية
 * @returns {string} المدة المنسقة
 */
function formatDuration(ms) {
  const seconds = Math.floor((ms / 1000) % 60);
  const minutes = Math.floor((ms / (1000 * 60)) % 60);
  const hours = Math.floor((ms / (1000 * 60 * 60)) % 24);
  const days = Math.floor(ms / (1000 * 60 * 60 * 24));
  
  const parts = [];
  if (days > 0) parts.push(`${days} يوم`);
  if (hours > 0) parts.push(`${hours} ساعة`);
  if (minutes > 0) parts.push(`${minutes} دقيقة`);
  if (seconds > 0) parts.push(`${seconds} ثانية`);
  
  return parts.join(' و ') || '0 ثانية';
}

module.exports = {
  formatNumber,
  formatDate,
  formatDuration
};
