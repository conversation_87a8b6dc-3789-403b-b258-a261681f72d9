declare const _default: "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<!DOCTYPE schema SYSTEM \"XMLSchema.dtd\"\n [\n   <!ATTLIST schema\n     xmlns:xenc CDATA #FIXED 'http://www.w3.org/2001/04/xmlenc#'\n     xmlns:ds CDATA #FIXED 'http://www.w3.org/2000/09/xmldsig#'>\n   <!ENTITY xenc 'http://www.w3.org/2001/04/xmlenc#'>\n   <!ENTITY % p ''>\n   <!ENTITY % s ''>\n  ]>\n\n<schema xmlns='http://www.w3.org/2001/XMLSchema' version='1.0'\n        xmlns:xenc='http://www.w3.org/2001/04/xmlenc#'\n        xmlns:ds='http://www.w3.org/2000/09/xmldsig#'\n        targetNamespace='http://www.w3.org/2001/04/xmlenc#'\n        elementFormDefault='qualified'>\n\n  <import namespace='http://www.w3.org/2000/09/xmldsig#'\n          schemaLocation='xmldsig-core-schema.xsd'/>\n\n  <complexType name='EncryptedType' abstract='true'>\n    <sequence>\n      <element name='EncryptionMethod' type='xenc:EncryptionMethodType'\n       minOccurs='0'/>\n      <element ref='ds:KeyInfo' minOccurs='0'/>\n      <element ref='xenc:CipherData'/>\n      <element ref='xenc:EncryptionProperties' minOccurs='0'/>\n    </sequence>\n    <attribute name='Id' type='ID' use='optional'/>\n    <attribute name='Type' type='anyURI' use='optional'/>\n    <attribute name='MimeType' type='string' use='optional'/>\n    <attribute name='Encoding' type='anyURI' use='optional'/>\n  </complexType>\n  \n  <complexType name='EncryptionMethodType' mixed='true'>\n    <sequence>\n      <element name='KeySize' minOccurs='0' type='xenc:KeySizeType'/>\n      <element name='OAEPparams' minOccurs='0' type='base64Binary'/>\n      <any namespace='##other' minOccurs='0' maxOccurs='unbounded'/>\n    </sequence>\n    <attribute name='Algorithm' type='anyURI' use='required'/>\n  </complexType>\n\n    <simpleType name='KeySizeType'>\n      <restriction base=\"integer\"/>\n    </simpleType>\n\n  <element name='CipherData' type='xenc:CipherDataType'/>\n  <complexType name='CipherDataType'>\n     <choice>\n       <element name='CipherValue' type='base64Binary'/>\n       <element ref='xenc:CipherReference'/>\n     </choice>\n    </complexType>\n\n   <element name='CipherReference' type='xenc:CipherReferenceType'/>\n   <complexType name='CipherReferenceType'>\n       <choice>\n         <element name='Transforms' type='xenc:TransformsType' minOccurs='0'/>\n       </choice>\n       <attribute name='URI' type='anyURI' use='required'/>\n   </complexType>\n\n     <complexType name='TransformsType'>\n       <sequence>\n         <element ref='ds:Transform' maxOccurs='unbounded'/>\n       </sequence>\n     </complexType>\n\n\n  <element name='EncryptedData' type='xenc:EncryptedDataType'/>\n  <complexType name='EncryptedDataType'>\n    <complexContent>\n      <extension base='xenc:EncryptedType'>\n       </extension>\n    </complexContent>\n  </complexType>\n\n  <!-- Children of ds:KeyInfo -->\n\n  <element name='EncryptedKey' type='xenc:EncryptedKeyType'/>\n  <complexType name='EncryptedKeyType'>\n    <complexContent>\n      <extension base='xenc:EncryptedType'>\n        <sequence>\n          <element ref='xenc:ReferenceList' minOccurs='0'/>\n          <element name='CarriedKeyName' type='string' minOccurs='0'/>\n        </sequence>\n        <attribute name='Recipient' type='string'\n         use='optional'/>\n      </extension>\n    </complexContent>\n  </complexType>\n\n    <element name=\"AgreementMethod\" type=\"xenc:AgreementMethodType\"/>\n    <complexType name=\"AgreementMethodType\" mixed=\"true\">\n      <sequence>\n        <element name=\"KA-Nonce\" minOccurs=\"0\" type=\"base64Binary\"/>\n        <!-- <element ref=\"ds:DigestMethod\" minOccurs=\"0\"/> -->\n        <any namespace=\"##other\" minOccurs=\"0\" maxOccurs=\"unbounded\"/>\n        <element name=\"OriginatorKeyInfo\" minOccurs=\"0\" type=\"ds:KeyInfoType\"/>\n        <element name=\"RecipientKeyInfo\" minOccurs=\"0\" type=\"ds:KeyInfoType\"/>\n      </sequence>\n      <attribute name=\"Algorithm\" type=\"anyURI\" use=\"required\"/>\n    </complexType>\n\n  <!-- End Children of ds:KeyInfo -->\n\n  <element name='ReferenceList'>\n    <complexType>\n      <choice minOccurs='1' maxOccurs='unbounded'>\n        <element name='DataReference' type='xenc:ReferenceType'/>\n        <element name='KeyReference' type='xenc:ReferenceType'/>\n      </choice>\n    </complexType>\n  </element>\n\n  <complexType name='ReferenceType'>\n    <sequence>\n      <any namespace='##other' minOccurs='0' maxOccurs='unbounded'/>\n    </sequence>\n    <attribute name='URI' type='anyURI' use='required'/>\n  </complexType>\n\n\n  <element name='EncryptionProperties' type='xenc:EncryptionPropertiesType'/>\n  <complexType name='EncryptionPropertiesType'>\n    <sequence>\n      <element ref='xenc:EncryptionProperty' maxOccurs='unbounded'/>\n    </sequence>\n    <attribute name='Id' type='ID' use='optional'/>\n  </complexType>\n\n    <element name='EncryptionProperty' type='xenc:EncryptionPropertyType'/>\n    <complexType name='EncryptionPropertyType' mixed='true'>\n      <choice maxOccurs='unbounded'>\n        <any namespace='##other' processContents='lax'/>\n      </choice>\n      <attribute name='Target' type='anyURI' use='optional'/>\n      <attribute name='Id' type='ID' use='optional'/>\n      <anyAttribute namespace=\"http://www.w3.org/XML/1998/namespace\"/>\n    </complexType>\n\n</schema>";
export default _default;
