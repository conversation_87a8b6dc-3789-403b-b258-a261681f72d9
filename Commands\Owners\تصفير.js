const { MessageEmbed } = require('discord.js');
const Data = require('pro.db');
const { owners } = require(`${process.cwd()}/config`);

module.exports = {
    name: "تصفير",
    description: "تصفير رصيد عضو في البنك",
    run: async (client, message, args) => {
        if (!owners.includes(message.author.id)) return message.react('❌');

        const targetUser = message.mentions.users.first();
        if (!targetUser) return message.reply("**منشن الي تبي تصفرة**");

        // تصفير رصيد العضو
        await Data.set(`money_${targetUser.id}`, 0);

        message.react("☑️");

        // تسجيل اللوج
        const logChannelId = await Data.get(`logChannel_${message.guild.id}`);
        const logChannel = message.guild.channels.cache.get(logChannelId);

        if (logChannel) {
            const logEmbed = new MessageEmbed()
                .setTitle("تصفير رصيد")
                .setDescription(`**تم تصفير رصيد : <@${targetUser.id}> 
                    من قبل : <@${message.author.id}>**`)
                .setThumbnail('https://e.top4top.io/p_31278dayn1.png')
               
                .setColor("#000")
                .setFooter({ text: 'Sky Store', iconURL: 'https://k.top4top.io/p_3299aejba1.png' });
        
            logChannel.send({ embeds: [logEmbed] });
        }        
    }
};
