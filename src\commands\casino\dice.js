const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('نرد')
    .setDescription('لعب النرد بمبلغ معين وتخمين الرقم')
    .addIntegerOption(option => 
      option.setName('المبلغ')
        .setDescription('المبلغ المراد المراهنة به')
        .setRequired(true)
        .setMinValue(50))
    .addIntegerOption(option => 
      option.setName('الرقم')
        .setDescription('الرقم الذي تراهن عليه (1-6)')
        .setRequired(true)
        .setMinValue(1)
        .setMaxValue(6)),
  
  async execute(interaction) {
    await interaction.deferReply();
    
    const amount = interaction.options.getInteger('المبلغ');
    const chosenNumber = interaction.options.getInteger('الرقم');
    
    try {
      // البحث عن حساب المستخدم
      let userProfile = await User.findOne({ userId: interaction.user.id });
      
      if (!userProfile) {
        // إنشاء حساب جديد للمستخدم إذا لم يكن موجودًا
        userProfile = await User.create({
          userId: interaction.user.id,
          username: interaction.user.username,
          balance: 1000,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // التحقق من وجود رصيد كافٍ
      if (userProfile.balance < amount) {
        return interaction.editReply(`❌ ليس لديك رصيد كافٍ! رصيدك الحالي هو ${formatNumber(userProfile.balance)} 💲`);
      }
      
      // رمي النرد
      const diceResult = Math.floor(Math.random() * 6) + 1;
      
      // تحديد النتيجة
      const won = diceResult === chosenNumber;
      
      // حساب المبلغ المربوح/المخسور
      if (won) {
        // المضاعف هو 6 لأن احتمالية الفوز هي 1/6
        const winAmount = amount * 6;
        userProfile.balance += winAmount - amount; // إضافة الربح مع خصم المبلغ الأصلي
      } else {
        userProfile.balance -= amount; // خصم المبلغ الأصلي
      }
      
      // حفظ التغييرات
      await userProfile.save();
      
      // تسجيل المعاملة
      logTransaction({
        type: 'dice',
        userId: interaction.user.id,
        username: interaction.user.username,
        amount: amount,
        chosenNumber: chosenNumber,
        diceResult: diceResult,
        result: won ? 'win' : 'lose',
        profit: won ? amount * 6 - amount : -amount,
        timestamp: new Date()
      });
      
      // تحديد الإيموجي للنرد
      const diceEmoji = ['⚀', '⚁', '⚂', '⚃', '⚄', '⚅'][diceResult - 1];
      
      // إنشاء رسالة النتيجة
      const embed = new EmbedBuilder()
        .setTitle('🎲 لعبة النرد')
        .setColor(won ? '#4CAF50' : '#F44336')
        .setDescription(`تم رمي النرد... ${diceEmoji}`)
        .addFields(
          { name: 'رقمك المختار', value: `${chosenNumber}`, inline: true },
          { name: 'نتيجة النرد', value: `${diceResult}`, inline: true },
          { name: 'النتيجة', value: won ? `🎉 فوز! +${formatNumber(amount * 6 - amount)} 💲` : `❌ خسارة -${formatNumber(amount)} 💲`, inline: true },
          { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
        )
        .setFooter({ text: won ? 'مبروك! 🎉' : 'حظًا أوفر في المرة القادمة!' })
        .setTimestamp();
      
      await interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error('خطأ في أمر النرد:', error);
      await interaction.editReply('حدث خطأ أثناء تنفيذ لعبة النرد. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
