require('dotenv').config();
const { Client, GatewayIntentBits, Collection } = require('discord.js');
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');

// إنشاء عميل جديد مع الصلاحيات المطلوبة
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildPresences,
    GatewayIntentBits.GuildMessageReactions
  ]
});

// إعداد متغيرات عامة
client.config = {
  prefix: process.env.PREFIX || '!', // البرفكس الافتراضي
  adminIds: (process.env.ADMIN_IDS || '').split(',')
};

// إعداد مجموعات للأوامر
client.commands = new Collection();
client.aliases = new Collection();
client.cooldowns = new Collection();

// اتصال بقاعدة البيانات
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('🟢 تم الاتصال بقاعدة البيانات بنجاح'))
.catch(err => console.error('🔴 خطأ في الاتصال بقاعدة البيانات:', err));

// تحميل الأوامر
const commandsPath = path.join(__dirname, 'commands');
const commandFolders = fs.readdirSync(commandsPath);

console.log('🔄 جاري تحميل الأوامر...');
for (const folder of commandFolders) {
  const folderPath = path.join(commandsPath, folder);
  const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));
  
  console.log(`📁 تحميل أوامر من المجلد: ${folder}`);
  for (const file of commandFiles) {
    const filePath = path.join(folderPath, file);
    const command = require(filePath);
    
    if ('name' in command && 'execute' in command) {
      client.commands.set(command.name, command);
      console.log(`✅ تم تحميل الأمر: ${command.name}`);
      
      // تحميل الاختصارات إذا كانت موجودة
      if (command.aliases && Array.isArray(command.aliases)) {
        command.aliases.forEach(alias => {
          client.aliases.set(alias, command.name);
          console.log(`  ↪️ اختصار: ${alias}`);
        });
      }
    } else {
      console.log(`⚠️ الأمر في ${filePath} يفتقد إلى خاصية name أو execute المطلوبة.`);
    }
  }
}

// تحميل الأحداث
const eventsPath = path.join(__dirname, 'events');
const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

console.log('🔄 جاري تحميل الأحداث...');
for (const file of eventFiles) {
  const filePath = path.join(eventsPath, file);
  const event = require(filePath);
  
  if (event.once) {
    client.once(event.name, (...args) => event.execute(client, ...args));
    console.log(`✅ تم تحميل الحدث (مرة واحدة): ${event.name}`);
  } else {
    client.on(event.name, (...args) => event.execute(client, ...args));
    console.log(`✅ تم تحميل الحدث: ${event.name}`);
  }
}

// تسجيل الدخول بالرمز المميز للبوت
client.login(process.env.DISCORD_TOKEN)
  .then(() => {
    console.log('🟢 تم تسجيل دخول البوت بنجاح');
    console.log(`🤖 البوت جاهز | البرفكس: ${client.config.prefix}`);
  })
  .catch(err => {
    console.error('🔴 خطأ في تسجيل دخول البوت:', err);
  });

// إعداد عملية الإغلاق الآمن
process.on('SIGINT', async () => {
  console.log('🔄 جاري إيقاف البوت...');
  await mongoose.connection.close();
  console.log('🟠 تم إغلاق الاتصال بقاعدة البيانات');
  client.destroy();
  console.log('🔴 تم إيقاف البوت');
  process.exit(0);
});
