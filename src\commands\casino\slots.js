const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const User = require('../../models/User');
const { formatNumber } = require('../../utils/formatters');
const { logTransaction } = require('../../utils/logger');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('سلوتس')
    .setDescription('لعب ماكينة السلوتس بمبلغ معين')
    .addIntegerOption(option => 
      option.setName('المبلغ')
        .setDescription('المبلغ المراد المراهنة به')
        .setRequired(true)
        .setMinValue(50)),
  
  async execute(interaction) {
    await interaction.deferReply();
    
    const amount = interaction.options.getInteger('المبلغ');
    
    try {
      // البحث عن حساب المستخدم
      let userProfile = await User.findOne({ userId: interaction.user.id });
      
      if (!userProfile) {
        // إنشاء حساب جديد للمستخدم إذا لم يكن موجودًا
        userProfile = await User.create({
          userId: interaction.user.id,
          username: interaction.user.username,
          balance: 1000,
          bank: 0,
          lastDaily: null,
          inventory: [],
          createdAt: new Date()
        });
      }
      
      // التحقق من وجود رصيد كافٍ
      if (userProfile.balance < amount) {
        return interaction.editReply(`❌ ليس لديك رصيد كافٍ! رصيدك الحالي هو ${formatNumber(userProfile.balance)} 💲`);
      }
      
      // تنفيذ لعبة السلوتس
      const slots = ['🍒', '🍊', '🍋', '🍇', '🍉', '🍓', '💎', '🎰', '💰'];
      const results = [];
      
      // توليد 3 رموز عشوائية
      for (let i = 0; i < 3; i++) {
        results.push(slots[Math.floor(Math.random() * slots.length)]);
      }
      
      // حساب المكافأة بناءً على النتائج
      let multiplier = 0;
      
      // إذا كانت جميع الرموز متطابقة
      if (results[0] === results[1] && results[1] === results[2]) {
        if (results[0] === '💎') {
          multiplier = 10; // جائزة كبرى للماس
        } else if (results[0] === '🎰' || results[0] === '💰') {
          multiplier = 7; // مكافأة عالية لرموز الكازينو
        } else {
          multiplier = 5; // مكافأة عادية للفواكه
        }
      }
      // إذا كان هناك رمزان متطابقان
      else if (results[0] === results[1] || results[1] === results[2] || results[0] === results[2]) {
        multiplier = 2;
      }
      
      // حساب المبلغ المربوح أو المخسور
      let winAmount = 0;
      let won = false;
      
      if (multiplier > 0) {
        winAmount = amount * multiplier;
        userProfile.balance += winAmount - amount; // إضافة المبلغ المربوح مع خصم المبلغ الأصلي
        won = true;
      } else {
        userProfile.balance -= amount; // خصم المبلغ الأصلي
      }
      
      // حفظ التغييرات
      await userProfile.save();
      
      // تسجيل المعاملة
      logTransaction({
        type: 'slots',
        userId: interaction.user.id,
        username: interaction.user.username,
        amount: amount,
        result: won ? 'win' : 'lose',
        profit: won ? winAmount - amount : -amount,
        timestamp: new Date()
      });
      
      // إنشاء رسالة النتيجة
      const resultDisplay = `${results[0]} | ${results[1]} | ${results[2]}`;
      
      const embed = new EmbedBuilder()
        .setTitle('🎰 ماكينة السلوتس')
        .setColor(won ? '#4CAF50' : '#F44336')
        .setDescription(`**${resultDisplay}**`)
        .addFields(
          { name: 'المبلغ المراهن به', value: `${formatNumber(amount)} 💲`, inline: true },
          { name: 'المضاعف', value: `${multiplier}x`, inline: true },
          { name: 'النتيجة', value: won ? `🎉 فوز! +${formatNumber(winAmount - amount)} 💲` : `❌ خسارة -${formatNumber(amount)} 💲`, inline: true },
          { name: 'رصيدك الحالي', value: `${formatNumber(userProfile.balance)} 💲`, inline: true }
        )
        .setFooter({ text: won ? 'مبروك! 🎉' : 'حظًا أوفر في المرة القادمة!' })
        .setTimestamp();
      
      await interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error('خطأ في أمر السلوتس:', error);
      await interaction.editReply('حدث خطأ أثناء تنفيذ لعبة السلوتس. الرجاء المحاولة مرة أخرى لاحقًا.');
    }
  }
};
